# polystack Configuration
daemon:
  pid_file: /var/run/polystack/polystackd.pid
  working_dir: /var/lib/polystack

grpc:
  address: ":7777"
  max_recv_msg_size: 10485760
  max_send_msg_size: 10485760

http:
  address: ":8080"
  read_timeout: 30s
  write_timeout: 30s

metrics:
  address: ":9090"
  path: /metrics

log:
  level: info
  format: json
  output: stdout

security:
  tls:
    enabled: true
    cert_file: /etc/polystack/certs/server.crt
    key_file: /etc/polystack/certs/server.key
    ca_file: /etc/polystack/certs/ca.crt
  
  encryption:
    enabled: true
    algorithm: aes-256-gcm

storage:
  backend: etcd
  etcd:
    endpoints:
      - http://localhost:2379
    dial_timeout: 5s
    
  postgres:
    dsn: postgres://polystack:password@localhost/polystack?sslmode=disable
    
components:
  registry: https://registry.polystack.io
  cache_dir: /var/cache/polystack/components
