syntax = "proto3";

package polystack.api.v1;

option go_package = "gitlab.thalesdigital.io/*********************/spikes/polystack/internal/api/v1;api";

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

service polystackService {
  // Component operations
  rpc ListComponents(ListComponentsRequest) returns (ListComponentsResponse) {
    option (google.api.http) = {
      get: "/v1/components"
    };
  }
  
  rpc GetComponent(GetComponentRequest) returns (Component) {
    option (google.api.http) = {
      get: "/v1/components/{name}"
    };
  }
  
  rpc InstallComponent(InstallComponentRequest) returns (stream InstallComponentResponse) {
    option (google.api.http) = {
      post: "/v1/components/{component}/install"
      body: "*"
    };
  }
  
  rpc ConfigureComponent(ConfigureComponentRequest) returns (stream ConfigureComponentResponse) {
    option (google.api.http) = {
      post: "/v1/components/{component}/configure"
      body: "*"
    };
  }
  
  rpc UninstallComponent(UninstallComponentRequest) returns (stream UninstallComponentResponse) {
    option (google.api.http) = {
      delete: "/v1/components/{component}"
    };
  }
  
  // Operations
  rpc ListOperations(ListOperationsRequest) returns (ListOperationsResponse) {
    option (google.api.http) = {
      get: "/v1/operations"
    };
  }
  
  rpc GetOperation(GetOperationRequest) returns (Operation) {
    option (google.api.http) = {
      get: "/v1/operations/{id}"
    };
  }
  
  // Logs
  rpc StreamLogs(StreamLogsRequest) returns (stream LogEntry) {
    option (google.api.http) = {
      get: "/v1/logs/stream"
    };
  }
  
  // Health
  rpc Health(google.protobuf.Empty) returns (HealthResponse) {
    option (google.api.http) = {
      get: "/v1/health"
    };
  }
}

message Component {
  string name = 1;
  string version = 2;
  string status = 3;
  google.protobuf.Timestamp installed_at = 4;
  map<string, string> endpoints = 5;
  ComponentHealth health = 6;
}

message ComponentHealth {
  bool healthy = 1;
  string message = 2;
  google.protobuf.Timestamp last_check = 3;
}

// Request messages
message ListComponentsRequest {
  string filter = 1;
  int32 page_size = 2;
  string page_token = 3;
}

message ListComponentsResponse {
  repeated Component components = 1;
  string next_page_token = 2;
}

message GetComponentRequest {
  string name = 1;
}

message InstallComponentRequest {
  string component = 1;
  string version = 2;
  map<string, string> config = 3;
  bool dry_run = 4;
}

message InstallComponentResponse {
  string operation_id = 1;
  string status = 2;
  string message = 3;
  int32 progress = 4;
  google.protobuf.Timestamp timestamp = 5;
}

message ConfigureComponentRequest {
  string component = 1;
  map<string, string> config = 2;
  bool restart_required = 3;
}

message ConfigureComponentResponse {
  string operation_id = 1;
  string status = 2;
  string message = 3;
  int32 progress = 4;
  google.protobuf.Timestamp timestamp = 5;
}

message UninstallComponentRequest {
  string component = 1;
  bool force = 2;
  bool cleanup_data = 3;
}

message UninstallComponentResponse {
  string operation_id = 1;
  string status = 2;
  string message = 3;
  int32 progress = 4;
  google.protobuf.Timestamp timestamp = 5;
}

message ListOperationsRequest {
  string component = 1;
  string status = 2;
  int32 page_size = 3;
  string page_token = 4;
}

message ListOperationsResponse {
  repeated Operation operations = 1;
  string next_page_token = 2;
}

message GetOperationRequest {
  string id = 1;
}

message Operation {
  string id = 1;
  string component = 2;
  string type = 3;
  string status = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
  string error_message = 7;
  int32 progress = 8;
}

message StreamLogsRequest {
  string component = 1;
  string container = 2;
  bool follow = 3;
  int32 tail_lines = 4;
  google.protobuf.Timestamp since = 5;
}

message LogEntry {
  google.protobuf.Timestamp timestamp = 1;
  string component = 2;
  string container = 3;
  string level = 4;
  string message = 5;
  map<string, string> labels = 6;
}

message HealthResponse {
  string status = 1;
  map<string, ComponentHealth> components = 2;
  google.protobuf.Timestamp timestamp = 3;
  string version = 4;
}
