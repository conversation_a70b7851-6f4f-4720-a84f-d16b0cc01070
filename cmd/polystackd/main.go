package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"runtime"
	"syscall"

	"github.com/spf13/cobra"
	"go.uber.org/zap"

	"gitlab.thalesdigital.io/*********************/spikes/polystack/internal/config"
	"gitlab.thalesdigital.io/*********************/spikes/polystack/internal/daemon"
)

var (
	version   = "dev"
	buildTime = "unknown"
	gitCommit = "unknown"
)

func main() {
	var cfgFile string

	rootCmd := &cobra.Command{
		Use:   "polystackd",
		Short: "polystack Daemon - Kubernetes Application Stack Manager",
		Long: `polystack Daemon (polystackd) is the core service that manages the lifecycle
of Kubernetes applications with features including
air-gapped deployments, multi-architecture support, and comprehensive
security controls.`,
		Version: fmt.Sprintf("%s (built %s, commit %s)", version, buildTime, gitCommit),
		RunE: func(cmd *cobra.Command, args []string) error {
			return runDaemon(cfgFile)
		},
	}

	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "/etc/polystack/config.yaml", "config file")
	rootCmd.AddCommand(
		newVersionCommand(),
		newHealthCommand(),
	)

	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintln(os.Stderr, err)
		os.Exit(1)
	}
}

func runDaemon(cfgFile string) error {
	// Load configuration
	cfg, err := config.Load(cfgFile)
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	// Initialize logger
	logger, err := initLogger(cfg.Log)
	if err != nil {
		return fmt.Errorf("failed to init logger: %w", err)
	}
	defer logger.Sync()

	// Create daemon
	d, err := daemon.New(cfg, logger)
	if err != nil {
		return fmt.Errorf("failed to create daemon: %w", err)
	}

	// Setup signal handling
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigCh
		logger.Info("Received shutdown signal")
		cancel()
	}()

	// Start daemon
	logger.Info("Starting polystack daemon",
		zap.String("version", version),
		zap.String("build_time", buildTime),
		zap.String("commit", gitCommit),
	)

	if err := d.Start(ctx); err != nil {
		return fmt.Errorf("daemon failed: %w", err)
	}

	return nil
}

func initLogger(cfg config.LogConfig) (*zap.Logger, error) {
	var zapCfg zap.Config

	if cfg.Format == "json" {
		zapCfg = zap.NewProductionConfig()
	} else {
		zapCfg = zap.NewDevelopmentConfig()
	}

	// Set log level
	if err := zapCfg.Level.UnmarshalText([]byte(cfg.Level)); err != nil {
		return nil, err
	}

	// Set output paths
	zapCfg.OutputPaths = []string{cfg.Output}
	if cfg.File.Enabled {
		zapCfg.OutputPaths = append(zapCfg.OutputPaths, cfg.File.Path)
	}

	return zapCfg.Build()
}

func newVersionCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "version",
		Short: "Print version information",
		Run: func(cmd *cobra.Command, args []string) {
			fmt.Printf("polystack Daemon %s\n", version)
			fmt.Printf("Build Time: %s\n", buildTime)
			fmt.Printf("Git Commit: %s\n", gitCommit)
			fmt.Printf("Go Version: %s\n", runtime.Version())
			fmt.Printf("OS/Arch: %s/%s\n", runtime.GOOS, runtime.GOARCH)
		},
	}
}

func newHealthCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "health",
		Short: "Check daemon health",
		RunE: func(cmd *cobra.Command, args []string) error {
			// Implement health check
			return nil
		},
	}
}
