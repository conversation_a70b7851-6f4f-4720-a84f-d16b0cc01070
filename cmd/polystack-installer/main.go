package main

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
)

var (
	version = "dev"
)

func main() {
	rootCmd := &cobra.Command{
		Use:   "polystack-installer",
		Short: "polystack Installer",
		Long:  "Install polystack components in air-gapped or connected environments",
		Version: version,
		RunE: func(cmd *cobra.Command, args []string) error {
			return cmd.Help()
		},
	}

	rootCmd.AddCommand(
		newInstallCommand(),
		newUninstallCommand(),
		newUpgradeCommand(),
		newStatusCommand(),
	)

	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintln(os.Stderr, err)
		os.Exit(1)
	}
}

func newInstallCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "install",
		Short: "Install polystack",
		RunE: func(cmd *cobra.Command, args []string) error {
			fmt.Println("Installing polystack...")
			return nil
		},
	}
}

func newUninstallCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "uninstall",
		Short: "Uninstall polystack",
		RunE: func(cmd *cobra.Command, args []string) error {
			fmt.Println("Uninstalling polystack...")
			return nil
		},
	}
}

func newUpgradeCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "upgrade",
		Short: "Upgrade polystack",
		RunE: func(cmd *cobra.Command, args []string) error {
			fmt.Println("Upgrading polystack...")
			return nil
		},
	}
}

func newStatusCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "status",
		Short: "Check polystack status",
		RunE: func(cmd *cobra.Command, args []string) error {
			fmt.Println("Checking polystack status...")
			return nil
		},
	}
}