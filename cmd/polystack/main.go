package main

import (
	"context"
	"fmt"
	"os"

	"github.com/spf13/cobra"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	"gitlab.thalesdigital.io/*********************/spikes/polystack/internal/cli"
	"gitlab.thalesdigital.io/*********************/spikes/polystack/internal/tui"
)

var (
	version = "dev"
)

func main() {
	var (
		daemon   string
		insecure bool
	)

	rootCmd := &cobra.Command{
		Use:   "polystack",
		Short: "polystack - Kubernetes Application Stack Tool",
		Long: `polystack is an tool for managing Kubernetes application
stacks with support for air-gapped deployments, multi-architecture
builds, and comprehensive lifecycle management.`,
		Version: version,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runTUI(daemon, insecure)
		},
	}

	rootCmd.PersistentFlags().StringVar(&daemon, "daemon", "localhost:7777", "daemon address")
	rootCmd.PersistentFlags().BoolVar(&insecure, "insecure", false, "use insecure connection")

	// Add CLI subcommands
	rootCmd.AddCommand(
		cli.NewInstallCommand(),
		cli.NewStatusCommand(),
		cli.NewConfigureCommand(),
		cli.NewUpgradeCommand(),
		cli.NewUninstallCommand(),
		cli.NewBackupCommand(),
		cli.NewRestoreCommand(),
		cli.NewAirGapCommand(),
		cli.NewBuildCommand(),
	)

	if err := rootCmd.Execute(); err != nil {
		os.Exit(1)
	}
}

func runTUI(daemon string, insecureConn bool) error {
	// Connect to daemon
	opts := []grpc.DialOption{
		grpc.WithBlock(),
	}

	if insecureConn {
		opts = append(opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	} else {
		// Add TLS credentials
	}

	conn, err := grpc.DialContext(context.Background(), daemon, opts...)
	if err != nil {
		return fmt.Errorf("failed to connect to daemon: %w", err)
	}
	defer conn.Close()

	// Start TUI
	return tui.Run(conn)
}
