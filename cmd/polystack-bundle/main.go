package main

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
)

var (
	version = "dev"
)

func main() {
	rootCmd := &cobra.Command{
		Use:   "polystack-bundle",
		Short: "polystack Bundle Creator",
		Long:  "Create and manage polystack deployment bundles for air-gapped environments",
		Version: version,
		RunE: func(cmd *cobra.Command, args []string) error {
			return cmd.Help()
		},
	}

	rootCmd.AddCommand(
		newCreateCommand(),
		newValidateCommand(),
		newExtractCommand(),
	)

	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintln(os.Stderr, err)
		os.Exit(1)
	}
}

func newCreateCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "create",
		Short: "Create a new bundle",
		RunE: func(cmd *cobra.Command, args []string) error {
			fmt.Println("Creating bundle...")
			return nil
		},
	}
}

func newValidateCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "validate",
		Short: "Validate a bundle",
		RunE: func(cmd *cobra.Command, args []string) error {
			fmt.Println("Validating bundle...")
			return nil
		},
	}
}

func newExtractCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "extract",
		Short: "Extract a bundle",
		RunE: func(cmd *cobra.Command, args []string) error {
			fmt.Println("Extracting bundle...")
			return nil
		},
	}
}