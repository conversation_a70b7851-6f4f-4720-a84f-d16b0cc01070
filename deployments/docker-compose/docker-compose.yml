version: "3.9"

services:
  polystackd:
    image: ${REGISTRY}/polystack/polystackd:${VERSION:-latest}
    container_name: polystackd
    hostname: polystackd
    ports:
      - "7777:7777"  # gRPC
      - "8080:8080"  # HTTP
      - "9090:9090"  # Metrics
    volumes:
      - polystackd-data:/var/lib/polystack
      - ./config:/etc/polystack
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      polystack_MODE: docker-compose
      polystack_LOG_LEVEL: ${LOG_LEVEL:-info}
    networks:
      - polystack
    restart: unless-stopped
    depends_on:
      etcd:
        condition: service_healthy
      postgres:
        condition: service_healthy
      vault:
        condition: service_started

  etcd:
    image: quay.io/coreos/etcd:v3.5.11
    container_name: etcd
    hostname: etcd
    environment:
      ETCD_NAME: etcd0
      ETCD_DATA_DIR: /etcd-data
      ETCD_LISTEN_CLIENT_URLS: http://0.0.0.0:2379
      ETCD_ADVERTISE_CLIENT_URLS: http://etcd:2379
      ETCD_LISTEN_PEER_URLS: http://0.0.0.0:2380
      ETCD_INITIAL_ADVERTISE_PEER_URLS: http://etcd:2380
      ETCD_INITIAL_CLUSTER: etcd0=http://etcd:2380
      ETCD_INITIAL_CLUSTER_TOKEN: polystack-etcd-cluster
      ETCD_INITIAL_CLUSTER_STATE: new
    volumes:
      - etcd-data:/etcd-data
    networks:
      - polystack
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 10s
      timeout: 5s
      retries: 5

  postgres:
    image: postgres:15-alpine
    container_name: postgres
    hostname: postgres
    environment:
      POSTGRES_USER: polystack
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: polystack
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - polystack
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U polystack"]
      interval: 10s
      timeout: 5s
      retries: 5

  vault:
    image: hashicorp/vault:latest
    container_name: vault
    hostname: vault
    cap_add:
      - IPC_LOCK
    environment:
      VAULT_DEV_ROOT_TOKEN_ID: ${VAULT_TOKEN}
      VAULT_DEV_LISTEN_ADDRESS: 0.0.0.0:8200
    networks:
      - polystack

networks:
  polystack:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  polystackd-data:
  etcd-data:
  postgres-data:
