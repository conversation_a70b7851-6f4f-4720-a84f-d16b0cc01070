apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: polystack-system

resources:
  - namespace.yaml
  - serviceaccount.yaml
  - rbac.yaml
  - configmap.yaml
  - secret.yaml
  - deployment.yaml
  - service.yaml
  - ingress.yaml

configMapGenerator:
  - name: polystack-config
    files:
      - config.yaml

secretGenerator:
  - name: polystack-secrets
    literals:
      - encryption-key=${polystack_ENCRYPTION_KEY}
      - vault-token=${VAULT_TOKEN}

images:
  - name: polystackd
    newName: ghcr.io/yourusername/polystack/polystackd
    newTag: latest
