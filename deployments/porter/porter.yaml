schemaVersion: 1.0.0-alpha.2
name: polystack
version: 0.1.0
description: polystack - Kubernetes Application Stack Tool
registry: ${REGISTRY}

credentials:
  - name: kubeconfig
    description: Kubernetes configuration file
    path: /home/<USER>/.kube/config
  
  - name: docker-config
    description: Docker configuration
    path: /home/<USER>/.docker/config.json

parameters:
  - name: installation-mode
    description: Installation mode (kubernetes, docker-compose, airgap)
    type: string
    default: kubernetes
    
  - name: deployment-profile
    description: Deployment profile (minimal, standard, full)
    type: string
    default: standard
    
  - name: platform
    description: Target platform
    type: string
    default: linux/amd64

outputs:
  - name: daemon-endpoint
    description: polystack daemon endpoint
    type: string
    
  - name: admin-password
    description: Admin password
    type: string
    sensitive: true

mixins:
  - exec
  - kubernetes

install:
  - exec:
      description: "Run preflight checks"
      command: /cnab/app/polystack-installer
      arguments:
        - preflight
        - --mode
        - ${bundle.parameters.installation-mode}
        
  - exec:
      description: "Install polystack"
      command: /cnab/app/polystack-installer
      arguments:
        - install
        - --mode
        - ${bundle.parameters.installation-mode}
        - --profile
        - ${bundle.parameters.deployment-profile}
        
  - exec:
      description: "Run postflight validation"
      command: /cnab/app/polystack-installer
      arguments:
        - postflight

upgrade:
  - exec:
      description: "Upgrade polystack"
      command: /cnab/app/polystack-installer
      arguments:
        - upgrade

uninstall:
  - exec:
      description: "Uninstall polystack"
      command: /cnab/app/polystack-installer
      arguments:
        - uninstall
