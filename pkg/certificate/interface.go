package certificate

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"time"
)

// Manager defines the interface for certificate management
type Manager interface {
	// CA operations
	GenerateCA(ctx context.Context, config CAConfig) (*Certificate, error)
	GetCA(ctx context.Context) (*Certificate, error)
	
	// Certificate operations
	IssueCertificate(ctx context.Context, config CertConfig) (*Certificate, error)
	GetCertificate(ctx context.Context, name string) (*Certificate, error)
	ListCertificates(ctx context.Context) ([]*Certificate, error)
	RevokeCertificate(ctx context.Context, name string) error
	
	// Rotation operations
	RotateCertificate(ctx context.Context, name string) (*Certificate, error)
	StartAutoRotation(ctx context.Context, interval time.Duration) error
	StopAutoRotation(ctx context.Context) error
	
	// TLS configuration
	GetServerTLSConfig(name string) (*tls.Config, error)
	GetClientTLSConfig(name string) (*tls.Config, error)
	
	// Watch for changes
	WatchCertificate(ctx context.Context, name string) (<-chan CertificateEvent, error)
	
	// Close the manager
	Close() error
}

// Certificate represents a X.509 certificate with its key
type Certificate struct {
	Name        string    `json:"name"`
	Type        CertType  `json:"type"`
	Certificate []byte    `json:"certificate"`
	PrivateKey  []byte    `json:"private_key"`
	CACert      []byte    `json:"ca_cert,omitempty"`
	IssuedAt    time.Time `json:"issued_at"`
	ExpiresAt   time.Time `json:"expires_at"`
	Serial      string    `json:"serial"`
	Subject     Subject   `json:"subject"`
	SANs        []string  `json:"sans,omitempty"`
	Revoked     bool      `json:"revoked"`
	RevokedAt   time.Time `json:"revoked_at,omitempty"`
}

// CertType represents the type of certificate
type CertType string

const (
	CertTypeCA     CertType = "ca"
	CertTypeServer CertType = "server"
	CertTypeClient CertType = "client"
)

// Subject represents certificate subject information
type Subject struct {
	CommonName         string   `json:"common_name"`
	Organization       []string `json:"organization,omitempty"`
	OrganizationalUnit []string `json:"organizational_unit,omitempty"`
	Country            []string `json:"country,omitempty"`
	Province           []string `json:"province,omitempty"`
	Locality           []string `json:"locality,omitempty"`
	StreetAddress      []string `json:"street_address,omitempty"`
	PostalCode         []string `json:"postal_code,omitempty"`
}

// CAConfig configuration for CA certificate generation
type CAConfig struct {
	Subject      Subject       `json:"subject"`
	ValidityDays int           `json:"validity_days"`
	KeySize      int           `json:"key_size"`
	KeyType      KeyType       `json:"key_type"`
}

// CertConfig configuration for certificate generation
type CertConfig struct {
	Name         string        `json:"name"`
	Subject      Subject       `json:"subject"`
	Type         CertType      `json:"type"`
	ValidityDays int           `json:"validity_days"`
	KeySize      int           `json:"key_size"`
	KeyType      KeyType       `json:"key_type"`
	SANs         []string      `json:"sans,omitempty"`
	IPSANs       []string      `json:"ip_sans,omitempty"`
}

// KeyType represents the type of cryptographic key
type KeyType string

const (
	KeyTypeRSA   KeyType = "rsa"
	KeyTypeECDSA KeyType = "ecdsa"
	KeyTypeEd25519 KeyType = "ed25519"
)

// CertificateEvent represents a certificate change event
type CertificateEvent struct {
	Type        EventType    `json:"type"`
	Certificate *Certificate `json:"certificate"`
	OldCert     *Certificate `json:"old_cert,omitempty"`
	Error       error        `json:"error,omitempty"`
	Timestamp   time.Time    `json:"timestamp"`
}

// EventType represents the type of certificate event
type EventType string

const (
	EventTypeIssued  EventType = "issued"
	EventTypeRotated EventType = "rotated"
	EventTypeRevoked EventType = "revoked"
	EventTypeExpired EventType = "expired"
)

// ParseResult represents the result of parsing a certificate
type ParseResult struct {
	Certificate *x509.Certificate
	PrivateKey  interface{}
	CACert      *x509.Certificate
}

// Helper functions

// ParseCertificate parses a Certificate into x509 structures
func ParseCertificate(cert *Certificate) (*ParseResult, error) {
	// Implementation will be in the main certificate file
	return nil, nil
}