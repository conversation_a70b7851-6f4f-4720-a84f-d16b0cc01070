package platform

import (
    "context"
    "testing"
    
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"
)

func TestDetector_Detect(t *testing.T) {
    tests := []struct {
        name     string
        setup    func()
        wantArch string
        wantErr  bool
    }{
        {
            name: "detect amd64",
            setup: func() {
                // Mock setup
            },
            wantArch: "amd64",
            wantErr:  false,
        },
        {
            name: "detect arm64",
            setup: func() {
                // Mock setup
            },
            wantArch: "arm64",
            wantErr:  false,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            if tt.setup != nil {
                tt.setup()
            }
            
            d := NewDetector()
            got, err := d.Detect(context.Background())
            
            if tt.wantErr {
                require.Error(t, err)
                return
            }
            
            require.NoError(t, err)
            assert.Equal(t, tt.wantArch, got.Arch)
        })
    }
}
