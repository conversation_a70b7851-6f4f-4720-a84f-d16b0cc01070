package platform

import (
    "context"
    "fmt"
    "runtime"
    "strings"
    "os"
    "os/exec"
)

type Platform struct {
    OS               string
    Arch             string
    Variant          string
    IsWSL            bool
    WSLVersion       int
    IsContainer      bool
    ContainerRuntime string
    Features         []string
}

type Detector struct {
    platform *Platform
}

func NewDetector() *Detector {
    return &Detector{
        platform: &Platform{
            OS:   runtime.GOOS,
            Arch: runtime.GOARCH,
        },
    }
}

func (d *Detector) Detect(ctx context.Context) (*Platform, error) {
    // Detect architecture variant
    d.detectArchVariant()
    
    // Detect WSL
    d.detectWSL()
    
    // Detect container environment
    d.detectContainer()
    
    // Detect CPU features
    d.detectCPUFeatures()
    
    return d.platform, nil
}
