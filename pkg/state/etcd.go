package state

import (
	"context"
	"encoding/json"
	"fmt"
	"path"
	"strings"
	"sync"
	"time"

	clientv3 "go.etcd.io/etcd/client/v3"
	"go.etcd.io/etcd/client/v3/concurrency"
	"go.uber.org/zap"
)

const (
	statePrefix     = "/polystack/state/"
	componentPrefix = "/polystack/components/"
	lockPrefix      = "/polystack/locks/"
	defaultTimeout  = 10 * time.Second
)

type etcdStateManager struct {
	client     *clientv3.Client
	logger     *zap.Logger
	session    *concurrency.Session
	watchers   map[string]chan struct{}
	watchersMu sync.RWMutex
}

// NewEtcdStateManager creates a new etcd-based state manager
func NewEtcdStateManager(endpoints []string, logger *zap.Logger) (Manager, error) {
	config := clientv3.Config{
		Endpoints:   endpoints,
		DialTimeout: 5 * time.Second,
		Logger:      logger.Named("etcd-client"),
	}

	client, err := clientv3.New(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create etcd client: %w", err)
	}

	// Create session for distributed locks
	session, err := concurrency.NewSession(client, concurrency.WithTTL(60))
	if err != nil {
		client.Close()
		return nil, fmt.Errorf("failed to create etcd session: %w", err)
	}

	return &etcdStateManager{
		client:   client,
		logger:   logger,
		session:  session,
		watchers: make(map[string]chan struct{}),
	}, nil
}

// GetComponentState retrieves the state of a specific component
func (m *etcdStateManager) GetComponentState(ctx context.Context, name string) (*ComponentState, error) {
	key := componentKey(name)
	
	ctx, cancel := context.WithTimeout(ctx, defaultTimeout)
	defer cancel()

	resp, err := m.client.Get(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to get component state: %w", err)
	}

	if len(resp.Kvs) == 0 {
		return nil, ErrComponentNotFound
	}

	var state ComponentState
	if err := json.Unmarshal(resp.Kvs[0].Value, &state); err != nil {
		return nil, fmt.Errorf("failed to unmarshal component state: %w", err)
	}

	return &state, nil
}

// SetComponentState updates the state of a component
func (m *etcdStateManager) SetComponentState(ctx context.Context, state *ComponentState) error {
	if state == nil {
		return fmt.Errorf("state cannot be nil")
	}

	key := componentKey(state.Name)
	state.UpdatedAt = time.Now()

	data, err := json.Marshal(state)
	if err != nil {
		return fmt.Errorf("failed to marshal component state: %w", err)
	}

	ctx, cancel := context.WithTimeout(ctx, defaultTimeout)
	defer cancel()

	_, err = m.client.Put(ctx, key, string(data))
	if err != nil {
		return fmt.Errorf("failed to set component state: %w", err)
	}

	m.logger.Debug("Component state updated",
		zap.String("component", state.Name),
		zap.String("status", string(state.Status)))

	return nil
}

// ListComponents returns all component states
func (m *etcdStateManager) ListComponents(ctx context.Context) ([]*ComponentState, error) {
	ctx, cancel := context.WithTimeout(ctx, defaultTimeout)
	defer cancel()

	resp, err := m.client.Get(ctx, componentPrefix, clientv3.WithPrefix())
	if err != nil {
		return nil, fmt.Errorf("failed to list component states: %w", err)
	}

	states := make([]*ComponentState, 0, len(resp.Kvs))
	for _, kv := range resp.Kvs {
		var state ComponentState
		if err := json.Unmarshal(kv.Value, &state); err != nil {
			m.logger.Warn("Failed to unmarshal component state",
				zap.String("key", string(kv.Key)),
				zap.Error(err))
			continue
		}
		states = append(states, &state)
	}

	return states, nil
}

// DeleteComponentState removes a component's state
func (m *etcdStateManager) DeleteComponentState(ctx context.Context, name string) error {
	key := componentKey(name)
	
	ctx, cancel := context.WithTimeout(ctx, defaultTimeout)
	defer cancel()

	resp, err := m.client.Delete(ctx, key)
	if err != nil {
		return fmt.Errorf("failed to delete component state: %w", err)
	}

	if resp.Deleted == 0 {
		return ErrComponentNotFound
	}

	m.logger.Info("Component state deleted", zap.String("component", name))
	return nil
}

// WatchComponent watches for changes to a specific component
func (m *etcdStateManager) WatchComponent(ctx context.Context, name string) (<-chan StateChange, error) {
	key := componentKey(name)
	changes := make(chan StateChange, 10)

	// Register watcher
	m.watchersMu.Lock()
	stopCh := make(chan struct{})
	m.watchers[name] = stopCh
	m.watchersMu.Unlock()

	watchCh := m.client.Watch(ctx, key)

	go func() {
		defer close(changes)
		defer func() {
			m.watchersMu.Lock()
			delete(m.watchers, name)
			m.watchersMu.Unlock()
		}()

		for {
			select {
			case <-ctx.Done():
				return
			case <-stopCh:
				return
			case resp := <-watchCh:
				if resp.Err() != nil {
					changes <- StateChange{
						Component: name,
						Error:     resp.Err(),
					}
					return
				}

				for _, event := range resp.Events {
					change := StateChange{
						Component: name,
						Timestamp: time.Now(),
					}

					switch event.Type {
					case clientv3.EventTypePut:
						var state ComponentState
						if err := json.Unmarshal(event.Kv.Value, &state); err != nil {
							change.Error = err
						} else {
							change.Type = ChangeTypeUpdate
							change.NewState = &state
						}
					case clientv3.EventTypeDelete:
						change.Type = ChangeTypeDelete
					}

					select {
					case changes <- change:
					case <-ctx.Done():
						return
					}
				}
			}
		}
	}()

	return changes, nil
}

// WatchAll watches for changes to all components
func (m *etcdStateManager) WatchAll(ctx context.Context) (<-chan StateChange, error) {
	changes := make(chan StateChange, 100)
	watchCh := m.client.Watch(ctx, componentPrefix, clientv3.WithPrefix())

	go func() {
		defer close(changes)

		for {
			select {
			case <-ctx.Done():
				return
			case resp := <-watchCh:
				if resp.Err() != nil {
					changes <- StateChange{
						Error: resp.Err(),
					}
					return
				}

				for _, event := range resp.Events {
					componentName := extractComponentName(string(event.Kv.Key))
					change := StateChange{
						Component: componentName,
						Timestamp: time.Now(),
					}

					switch event.Type {
					case clientv3.EventTypePut:
						var state ComponentState
						if err := json.Unmarshal(event.Kv.Value, &state); err != nil {
							change.Error = err
						} else {
							change.Type = ChangeTypeUpdate
							change.NewState = &state
						}
					case clientv3.EventTypeDelete:
						change.Type = ChangeTypeDelete
					}

					select {
					case changes <- change:
					case <-ctx.Done():
						return
					}
				}
			}
		}
	}()

	return changes, nil
}

// Transaction executes a function within a distributed transaction
func (m *etcdStateManager) Transaction(ctx context.Context, fn TransactionFunc) error {
	// Create a distributed lock
	lockKey := path.Join(lockPrefix, "transaction", fmt.Sprintf("%d", time.Now().UnixNano()))
	mutex := concurrency.NewMutex(m.session, lockKey)

	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// Acquire lock
	if err := mutex.Lock(ctx); err != nil {
		return fmt.Errorf("failed to acquire transaction lock: %w", err)
	}
	defer mutex.Unlock(ctx)

	// Execute transaction function
	return fn(ctx, m)
}

// CreateBackup creates a backup of all state data
func (m *etcdStateManager) CreateBackup(ctx context.Context, description string) (*Backup, error) {
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// Get all component states
	resp, err := m.client.Get(ctx, componentPrefix, clientv3.WithPrefix())
	if err != nil {
		return nil, fmt.Errorf("failed to backup state: %w", err)
	}

	// Create internal backup structure
	stateBackup := &StateBackup{
		Version:   "1.0",
		Timestamp: time.Now(),
		States:    make(map[string]*ComponentState),
	}

	for _, kv := range resp.Kvs {
		var state ComponentState
		if err := json.Unmarshal(kv.Value, &state); err != nil {
			m.logger.Warn("Failed to unmarshal state during backup",
				zap.String("key", string(kv.Key)),
				zap.Error(err))
			continue
		}
		componentName := extractComponentName(string(kv.Key))
		stateBackup.States[componentName] = &state
	}

	// Get metadata
	metaResp, err := m.client.Get(ctx, statePrefix+"metadata", clientv3.WithPrefix())
	if err == nil && len(metaResp.Kvs) > 0 {
		stateBackup.Metadata = make(map[string]string)
		for _, kv := range metaResp.Kvs {
			key := strings.TrimPrefix(string(kv.Key), statePrefix+"metadata/")
			stateBackup.Metadata[key] = string(kv.Value)
		}
	}

	// Marshal backup data
	backupData, err := json.Marshal(stateBackup)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal backup: %w", err)
	}

	// Create backup entry
	backupID := fmt.Sprintf("backup-%d", time.Now().Unix())
	backupKey := path.Join(statePrefix, "backups", backupID)
	
	backup := &Backup{
		ID:          backupID,
		Description: description,
		CreatedAt:   time.Now(),
		Size:        int64(len(backupData)),
		Components:  len(stateBackup.States),
	}

	// Store backup metadata
	backupMeta, err := json.Marshal(backup)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal backup metadata: %w", err)
	}

	// Store both backup data and metadata in a transaction
	txn := m.client.Txn(ctx)
	txn.Then(
		clientv3.OpPut(backupKey, string(backupData)),
		clientv3.OpPut(backupKey+".meta", string(backupMeta)),
	)
	
	if _, err := txn.Commit(); err != nil {
		return nil, fmt.Errorf("failed to store backup: %w", err)
	}

	return backup, nil
}

// RestoreBackup restores state from a backup
func (m *etcdStateManager) RestoreBackup(ctx context.Context, backupID string) error {
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// Retrieve backup data
	backupKey := path.Join(statePrefix, "backups", backupID)
	resp, err := m.client.Get(ctx, backupKey)
	if err != nil {
		return fmt.Errorf("failed to retrieve backup: %w", err)
	}

	if len(resp.Kvs) == 0 {
		return fmt.Errorf("backup not found: %s", backupID)
	}

	// Unmarshal backup data
	var backup StateBackup
	if err := json.Unmarshal(resp.Kvs[0].Value, &backup); err != nil {
		return fmt.Errorf("failed to unmarshal backup: %w", err)
	}

	return m.Transaction(ctx, func(ctx context.Context, sm Manager) error {
		// Clear existing states
		if _, err := m.client.Delete(ctx, componentPrefix, clientv3.WithPrefix()); err != nil {
			return fmt.Errorf("failed to clear existing states: %w", err)
		}

		// Restore component states
		for name, state := range backup.States {
			if err := sm.SetComponentState(ctx, state); err != nil {
				return fmt.Errorf("failed to restore component %s: %w", name, err)
			}
		}

		// Restore metadata
		for key, value := range backup.Metadata {
			metaKey := path.Join(statePrefix, "metadata", key)
			if _, err := m.client.Put(ctx, metaKey, value); err != nil {
				return fmt.Errorf("failed to restore metadata %s: %w", key, err)
			}
		}

		m.logger.Info("State restored from backup",
			zap.Time("backup_time", backup.Timestamp),
			zap.Int("components", len(backup.States)))

		return nil
	})
}

// ListBackups returns a list of all backups
func (m *etcdStateManager) ListBackups(ctx context.Context) ([]*Backup, error) {
	ctx, cancel := context.WithTimeout(ctx, defaultTimeout)
	defer cancel()

	// Get all backup metadata
	backupPrefix := path.Join(statePrefix, "backups")
	resp, err := m.client.Get(ctx, backupPrefix, clientv3.WithPrefix())
	if err != nil {
		return nil, fmt.Errorf("failed to list backups: %w", err)
	}

	backups := make([]*Backup, 0)
	for _, kv := range resp.Kvs {
		// Only process metadata entries
		if !strings.HasSuffix(string(kv.Key), ".meta") {
			continue
		}

		var backup Backup
		if err := json.Unmarshal(kv.Value, &backup); err != nil {
			m.logger.Warn("Failed to unmarshal backup metadata",
				zap.String("key", string(kv.Key)),
				zap.Error(err))
			continue
		}
		backups = append(backups, &backup)
	}

	return backups, nil
}

// Close closes the state manager and releases resources
func (m *etcdStateManager) Close() error {
	// Close all watchers
	m.watchersMu.Lock()
	for _, stopCh := range m.watchers {
		close(stopCh)
	}
	m.watchers = nil
	m.watchersMu.Unlock()

	// Close session
	if err := m.session.Close(); err != nil {
		m.logger.Warn("Failed to close etcd session", zap.Error(err))
	}

	// Close client
	if err := m.client.Close(); err != nil {
		return fmt.Errorf("failed to close etcd client: %w", err)
	}

	return nil
}

// Helper functions
func componentKey(name string) string {
	return path.Join(componentPrefix, name)
}

func extractComponentName(key string) string {
	return strings.TrimPrefix(key, componentPrefix)
}