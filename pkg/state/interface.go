package state

import (
    "context"
    "errors"
    "time"
)

var (
    // ErrComponentNotFound is returned when a component is not found
    ErrComponentNotFound = errors.New("component not found")
    
    // ErrInvalidState is returned when state data is invalid
    ErrInvalidState = errors.New("invalid state data")
)

type Manager interface {
    // Component state operations
    GetComponentState(ctx context.Context, name string) (*ComponentState, error)
    SetComponentState(ctx context.Context, state *ComponentState) error
    ListComponents(ctx context.Context) ([]*ComponentState, error)
    DeleteComponentState(ctx context.Context, name string) error
    
    // Watch operations
    WatchComponent(ctx context.Context, name string) (<-chan <PERSON>hange, error)
    WatchAll(ctx context.Context) (<-chan StateChange, error)
    
    // Transaction support
    Transaction(ctx context.Context, fn TransactionFunc) error
    
    // Backup operations
    CreateBackup(ctx context.Context, description string) (*Backup, error)
    RestoreBackup(ctx context.Context, backupID string) error
    ListBackups(ctx context.Context) ([]*Backup, error)
}

type ComponentState struct {
    Name            string                 `json:"name"`
    Version         string                 `json:"version"`
    Status          ComponentStatus        `json:"status"`
    InstalledAt     time.Time             `json:"installed_at"`
    LastConfigured  time.Time             `json:"last_configured"`
    UpdatedAt       time.Time             `json:"updated_at"`
    Configuration   map[string]interface{} `json:"configuration"`
    Credentials     map[string]string      `json:"credentials,omitempty"`
    Endpoints       map[string]string      `json:"endpoints"`
    Dependencies    []string               `json:"dependencies"`
    CertificateRefs []string               `json:"certificate_refs"`
}

type ComponentStatus string

const (
    StatusNotInstalled ComponentStatus = "not_installed"
    StatusInstalling   ComponentStatus = "installing"
    StatusInstalled    ComponentStatus = "installed"
    StatusConfiguring  ComponentStatus = "configuring"
    StatusReady        ComponentStatus = "ready"
    StatusError        ComponentStatus = "error"
    StatusUninstalling ComponentStatus = "uninstalling"
)

// StateChange represents a change in component state
type StateChange struct {
    Component string          `json:"component"`
    Type      ChangeType      `json:"type"`
    OldState  *ComponentState `json:"old_state,omitempty"`
    NewState  *ComponentState `json:"new_state,omitempty"`
    Timestamp time.Time       `json:"timestamp"`
    Error     error           `json:"error,omitempty"`
}

// ChangeType represents the type of state change
type ChangeType string

const (
    ChangeTypeCreate ChangeType = "create"
    ChangeTypeUpdate ChangeType = "update"
    ChangeTypeDelete ChangeType = "delete"
)

// TransactionFunc is a function that executes within a transaction
type TransactionFunc func(ctx context.Context, sm Manager) error

// StateBackup represents a backup of all state data
type StateBackup struct {
    Version   string                      `json:"version"`
    Timestamp time.Time                   `json:"timestamp"`
    States    map[string]*ComponentState  `json:"states"`
    Metadata  map[string]string           `json:"metadata,omitempty"`
}

// Backup represents a backup entry
type Backup struct {
    ID          string    `json:"id"`
    Description string    `json:"description"`
    CreatedAt   time.Time `json:"created_at"`
    Size        int64     `json:"size"`
    Components  int       `json:"components"`
}
