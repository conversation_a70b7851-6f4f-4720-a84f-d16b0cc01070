package state

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap/zaptest"
)

func TestEtcdStateManager_ComponentOperations(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test")
	}

	// This test requires etcd to be running
	// You can start it with: docker run -d -p 2379:2379 quay.io/coreos/etcd:latest
	
	logger := zaptest.NewLogger(t)
	manager, err := NewEtcdStateManager([]string{"localhost:2379"}, logger)
	require.NoError(t, err)
	defer manager.Close()

	ctx := context.Background()

	t.Run("ComponentNotFound", func(t *testing.T) {
		_, err := manager.GetComponentState(ctx, "nonexistent")
		assert.ErrorIs(t, err, ErrComponentNotFound)
	})

	t.Run("SetAndGetComponent", func(t *testing.T) {
		state := &ComponentState{
			Name:    "test-component",
			Version: "1.0.0",
			Status:  StatusInstalled,
			Configuration: map[string]interface{}{
				"port": 8080,
				"host": "localhost",
			},
			Endpoints: map[string]string{
				"api": "http://localhost:8080",
			},
		}

		err := manager.SetComponentState(ctx, state)
		require.NoError(t, err)

		retrieved, err := manager.GetComponentState(ctx, "test-component")
		require.NoError(t, err)
		assert.Equal(t, state.Name, retrieved.Name)
		assert.Equal(t, state.Version, retrieved.Version)
		assert.Equal(t, state.Status, retrieved.Status)
		assert.NotZero(t, retrieved.UpdatedAt)
	})

	t.Run("ListComponents", func(t *testing.T) {
		// Add another component
		state2 := &ComponentState{
			Name:    "test-component-2",
			Version: "2.0.0",
			Status:  StatusReady,
		}
		err := manager.SetComponentState(ctx, state2)
		require.NoError(t, err)

		components, err := manager.ListComponents(ctx)
		require.NoError(t, err)
		assert.GreaterOrEqual(t, len(components), 2)
	})

	t.Run("DeleteComponent", func(t *testing.T) {
		err := manager.DeleteComponentState(ctx, "test-component")
		require.NoError(t, err)

		_, err = manager.GetComponentState(ctx, "test-component")
		assert.ErrorIs(t, err, ErrComponentNotFound)
	})
}

func TestEtcdStateManager_Watch(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test")
	}

	logger := zaptest.NewLogger(t)
	manager, err := NewEtcdStateManager([]string{"localhost:2379"}, logger)
	require.NoError(t, err)
	defer manager.Close()

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	t.Run("WatchComponent", func(t *testing.T) {
		// Start watching
		changes, err := manager.WatchComponent(ctx, "watch-test")
		require.NoError(t, err)

		// Create component
		state := &ComponentState{
			Name:    "watch-test",
			Version: "1.0.0",
			Status:  StatusInstalling,
		}
		err = manager.SetComponentState(ctx, state)
		require.NoError(t, err)

		// Wait for change
		select {
		case change := <-changes:
			assert.Equal(t, "watch-test", change.Component)
			assert.Equal(t, ChangeTypeUpdate, change.Type)
			assert.NotNil(t, change.NewState)
			assert.Equal(t, StatusInstalling, change.NewState.Status)
		case <-time.After(5 * time.Second):
			t.Fatal("Timeout waiting for change")
		}

		// Update component
		state.Status = StatusReady
		err = manager.SetComponentState(ctx, state)
		require.NoError(t, err)

		// Wait for update
		select {
		case change := <-changes:
			assert.Equal(t, ChangeTypeUpdate, change.Type)
			assert.Equal(t, StatusReady, change.NewState.Status)
		case <-time.After(5 * time.Second):
			t.Fatal("Timeout waiting for change")
		}

		// Delete component
		err = manager.DeleteComponentState(ctx, "watch-test")
		require.NoError(t, err)

		// Wait for delete
		select {
		case change := <-changes:
			assert.Equal(t, ChangeTypeDelete, change.Type)
		case <-time.After(5 * time.Second):
			t.Fatal("Timeout waiting for change")
		}
	})
}

func TestEtcdStateManager_Transaction(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test")
	}

	logger := zaptest.NewLogger(t)
	manager, err := NewEtcdStateManager([]string{"localhost:2379"}, logger)
	require.NoError(t, err)
	defer manager.Close()

	ctx := context.Background()

	t.Run("SuccessfulTransaction", func(t *testing.T) {
		err := manager.Transaction(ctx, func(ctx context.Context, sm Manager) error {
			// Create multiple components atomically
			for i := 0; i < 3; i++ {
				state := &ComponentState{
					Name:    fmt.Sprintf("txn-component-%d", i),
					Version: "1.0.0",
					Status:  StatusInstalled,
				}
				if err := sm.SetComponentState(ctx, state); err != nil {
					return err
				}
			}
			return nil
		})
		require.NoError(t, err)

		// Verify all components were created
		for i := 0; i < 3; i++ {
			state, err := manager.GetComponentState(ctx, fmt.Sprintf("txn-component-%d", i))
			require.NoError(t, err)
			assert.Equal(t, StatusInstalled, state.Status)
		}
	})
}

func TestEtcdStateManager_Backup(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test")
	}

	logger := zaptest.NewLogger(t)
	manager, err := NewEtcdStateManager([]string{"localhost:2379"}, logger)
	require.NoError(t, err)
	defer manager.Close()

	ctx := context.Background()

	// Create test components
	components := []string{"backup-test-1", "backup-test-2", "backup-test-3"}
	for _, name := range components {
		state := &ComponentState{
			Name:    name,
			Version: "1.0.0",
			Status:  StatusReady,
			Configuration: map[string]interface{}{
				"test": true,
			},
		}
		err := manager.SetComponentState(ctx, state)
		require.NoError(t, err)
	}

	t.Run("CreateBackup", func(t *testing.T) {
		backup, err := manager.CreateBackup(ctx, "Test backup")
		require.NoError(t, err)
		assert.NotEmpty(t, backup.ID)
		assert.Equal(t, "Test backup", backup.Description)
		assert.GreaterOrEqual(t, backup.Components, 3)
		assert.Greater(t, backup.Size, int64(0))
	})

	t.Run("ListBackups", func(t *testing.T) {
		backups, err := manager.ListBackups(ctx)
		require.NoError(t, err)
		assert.NotEmpty(t, backups)
	})

	t.Run("RestoreBackup", func(t *testing.T) {
		// Create backup
		backup, err := manager.CreateBackup(ctx, "Restore test")
		require.NoError(t, err)

		// Delete one component
		err = manager.DeleteComponentState(ctx, "backup-test-1")
		require.NoError(t, err)

		// Restore from backup
		err = manager.RestoreBackup(ctx, backup.ID)
		require.NoError(t, err)

		// Verify component was restored
		state, err := manager.GetComponentState(ctx, "backup-test-1")
		require.NoError(t, err)
		assert.Equal(t, StatusReady, state.Status)
	})
}