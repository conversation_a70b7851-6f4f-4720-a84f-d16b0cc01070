// +build integration

package integration

import (
    "context"
    "testing"
    "time"
    
    "github.com/stretchr/testify/require"
    "google.golang.org/grpc"
    "google.golang.org/grpc/credentials/insecure"
)

func TestDaemonStartup(t *testing.T) {
    if testing.Short() {
        t.<PERSON><PERSON>("Skipping integration test")
    }
    
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()
    
    // Start daemon
    // ...
    
    // Connect to daemon
    conn, err := grpc.DialContext(ctx, "localhost:7777",
        grpc.WithTransportCredentials(insecure.NewCredentials()),
        grpc.WithBlock(),
    )
    require.NoError(t, err)
    defer conn.Close()
    
    // Test health endpoint
    // ...
}
