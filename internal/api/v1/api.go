package api

import (
	"context"
	"google.golang.org/grpc"
)

type PolystackServiceClient interface {
	GetComponents(ctx context.Context, req *GetComponentsRequest) (*GetComponentsResponse, error)
	GetOperations(ctx context.Context, req *GetOperationsRequest) (*GetOperationsResponse, error)
	InstallComponent(ctx context.Context, req *InstallComponentRequest) (*InstallComponentResponse, error)
}

type polystackServiceClientImpl struct {
	conn *grpc.ClientConn
}

func NewpolystackServiceClient(conn *grpc.ClientConn) PolystackServiceClient {
	return &polystackServiceClientImpl{conn: conn}
}

func (c *polystackServiceClientImpl) GetComponents(ctx context.Context, req *GetComponentsRequest) (*GetComponentsResponse, error) {
	return &GetComponentsResponse{}, nil
}

func (c *polystackServiceClientImpl) GetOperations(ctx context.Context, req *GetOperationsRequest) (*GetOperationsResponse, error) {
	return &GetOperationsResponse{}, nil
}

func (c *polystackServiceClientImpl) InstallComponent(ctx context.Context, req *InstallComponentRequest) (*InstallComponentResponse, error) {
	return &InstallComponentResponse{}, nil
}

type Component struct {
	Name        string            `json:"name"`
	Version     string            `json:"version"`
	Status      ComponentStatus   `json:"status"`
	Description string            `json:"description"`
	Dependencies []string         `json:"dependencies"`
	Config      map[string]string `json:"config"`
}

type ComponentStatus string

const (
	ComponentStatusInstalled   ComponentStatus = "installed"
	ComponentStatusNotInstalled ComponentStatus = "not_installed"
	ComponentStatusUpgrading   ComponentStatus = "upgrading"
	ComponentStatusError       ComponentStatus = "error"
)

type Operation struct {
	ID          string          `json:"id"`
	Type        OperationType   `json:"type"`
	Status      OperationStatus `json:"status"`
	Component   string          `json:"component"`
	Progress    float32         `json:"progress"`
	Error       string          `json:"error,omitempty"`
	StartedAt   int64           `json:"started_at"`
	CompletedAt int64           `json:"completed_at,omitempty"`
}

type OperationType string

const (
	OperationTypeInstall   OperationType = "install"
	OperationTypeUpgrade   OperationType = "upgrade"
	OperationTypeUninstall OperationType = "uninstall"
	OperationTypeBackup    OperationType = "backup"
	OperationTypeRestore   OperationType = "restore"
)

type OperationStatus string

const (
	OperationStatusPending   OperationStatus = "pending"
	OperationStatusRunning   OperationStatus = "running"
	OperationStatusCompleted OperationStatus = "completed"
	OperationStatusFailed    OperationStatus = "failed"
)

type GetComponentsRequest struct{}

type GetComponentsResponse struct {
	Components []*Component `json:"components"`
}

type GetOperationsRequest struct{}

type GetOperationsResponse struct {
	Operations []*Operation `json:"operations"`
}

type InstallComponentRequest struct {
	Name    string            `json:"name"`
	Version string            `json:"version"`
	Config  map[string]string `json:"config"`
}

type InstallComponentResponse struct {
	OperationID string `json:"operation_id"`
}