package config

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v3"
)

type Config struct {
	Server   ServerConfig   `yaml:"server"`
	Database DatabaseConfig `yaml:"database"`
	Log      LogConfig      `yaml:"log"`
	Security SecurityConfig `yaml:"security"`
	Features FeatureConfig  `yaml:"features"`
}

type ServerConfig struct {
	Host         string `yaml:"host"`
	Port         int    `yaml:"port"`
	GRPCPort     int    `yaml:"grpc_port"`
	MetricsPort  int    `yaml:"metrics_port"`
	ReadTimeout  string `yaml:"read_timeout"`
	WriteTimeout string `yaml:"write_timeout"`
	TLS          struct {
		Enabled  bool   `yaml:"enabled"`
		CertFile string `yaml:"cert_file"`
		KeyFile  string `yaml:"key_file"`
	} `yaml:"tls"`
}

type DatabaseConfig struct {
	Type     string `yaml:"type"`
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Name     string `yaml:"name"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	SSLMode  string `yaml:"ssl_mode"`
}

type LogConfig struct {
	Level  string `yaml:"level"`
	Format string `yaml:"format"`
	Output string `yaml:"output"`
	File   struct {
		Enabled bool   `yaml:"enabled"`
		Path    string `yaml:"path"`
		MaxSize int    `yaml:"max_size"`
		MaxAge  int    `yaml:"max_age"`
		Backup  int    `yaml:"backup"`
	} `yaml:"file"`
}

type SecurityConfig struct {
	Authentication struct {
		Enabled bool   `yaml:"enabled"`
		Type    string `yaml:"type"`
		OIDC    struct {
			Issuer       string `yaml:"issuer"`
			ClientID     string `yaml:"client_id"`
			ClientSecret string `yaml:"client_secret"`
		} `yaml:"oidc"`
	} `yaml:"authentication"`
	Authorization struct {
		Enabled bool   `yaml:"enabled"`
		RBAC    bool   `yaml:"rbac"`
		Policy  string `yaml:"policy"`
	} `yaml:"authorization"`
}

type FeatureConfig struct {
	AirGap struct {
		Enabled     bool   `yaml:"enabled"`
		Registry    string `yaml:"registry"`
		StoragePath string `yaml:"storage_path"`
	} `yaml:"airgap"`
	Monitoring struct {
		Enabled     bool   `yaml:"enabled"`
		Prometheus  bool   `yaml:"prometheus"`
		Jaeger      bool   `yaml:"jaeger"`
		MetricsPath string `yaml:"metrics_path"`
	} `yaml:"monitoring"`
	Backup struct {
		Enabled    bool   `yaml:"enabled"`
		Provider   string `yaml:"provider"`
		Schedule   string `yaml:"schedule"`
		Retention  string `yaml:"retention"`
		Encryption bool   `yaml:"encryption"`
	} `yaml:"backup"`
}

func Load(path string) (*Config, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var cfg Config
	if err := yaml.Unmarshal(data, &cfg); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	if err := validate(&cfg); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	return &cfg, nil
}

func validate(cfg *Config) error {
	if cfg.Server.Port <= 0 {
		return fmt.Errorf("server port must be greater than 0")
	}
	if cfg.Server.GRPCPort <= 0 {
		return fmt.Errorf("gRPC port must be greater than 0")
	}
	if cfg.Log.Level == "" {
		cfg.Log.Level = "info"
	}
	if cfg.Log.Format == "" {
		cfg.Log.Format = "json"
	}
	if cfg.Log.Output == "" {
		cfg.Log.Output = "stdout"
	}
	return nil
}
