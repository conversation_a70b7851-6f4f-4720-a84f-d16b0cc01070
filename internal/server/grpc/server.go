package grpc

import (
	"context"
	"net"

	"go.uber.org/zap"
	"google.golang.org/grpc"
)

type Server struct {
	logger *zap.Logger
	server *grpc.Server
}

func NewServer(logger *zap.Logger) *Server {
	return &Server{
		logger: logger,
		server: grpc.NewServer(),
	}
}

func (s *Server) Start(addr string) error {
	lis, err := net.Listen("tcp", addr)
	if err != nil {
		return err
	}

	s.logger.Info("Starting gRPC server", zap.String("addr", addr))
	return s.server.Serve(lis)
}

func (s *Server) Stop() {
	s.logger.Info("Stopping gRPC server")
	s.server.GracefulStop()
}