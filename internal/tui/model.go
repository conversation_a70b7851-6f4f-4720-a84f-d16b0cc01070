package tui

import (
	"fmt"

	"github.com/charmbracelet/bubbles/help"
	"github.com/charmbracelet/bubbles/list"
	"github.com/charmbracelet/bubbles/progress"
	"github.com/charmbracelet/bubbles/spinner"
	"github.com/charmbracelet/bubbles/table"
	"github.com/charmbracelet/bubbles/viewport"
	tea "github.com/charmbracelet/bubbletea"
	"gitlab.thalesdigital.io/*********************/spikes/polystack/internal/api/v1"
	"google.golang.org/grpc"
)

type Model struct {
	client api.PolystackServiceClient
	conn   *grpc.ClientConn

	// UI state
	view   View
	width  int
	height int

	// Components
	componentList  list.Model
	componentTable table.Model
	logViewer      viewport.Model
	progressBar    progress.Model
	spinner        spinner.Model
	help           help.Model

	// Data
	components   []*api.Component
	selectedComp *api.Component
	logs         []string
	operations   []*api.Operation

	// State
	loading bool
	error   error
	message string
}

type View int

const (
	ViewDashboard View = iota
	ViewComponents
	ViewOperations
	ViewLogs
	ViewInstall
	ViewConfigure
	ViewSettings
)

func NewModel(conn *grpc.ClientConn) *Model {
	m := &Model{
		client:  api.NewpolystackServiceClient(conn),
		conn:    conn,
		view:    ViewDashboard,
		spinner: spinner.New(),
		help:    help.New(),
	}

	return m
}

func Run(conn *grpc.ClientConn) error {
	model := NewModel(conn)
	p := tea.NewProgram(model, tea.WithAltScreen())

	if _, err := p.Run(); err != nil {
		return fmt.Errorf("TUI error: %w", err)
	}

	return nil
}

func (m *Model) Init() tea.Cmd {
	return tea.Batch(
		m.spinner.Tick,
		m.loadInitialData(),
	)
}

func (m *Model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		m.updateComponentSizes()

	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			return m, tea.Quit
		case "tab":
			m.nextView()
		case "shift+tab":
			m.prevView()
		case "enter":
			return m, m.handleEnter()
		}

	case spinner.TickMsg:
		m.spinner, cmd = m.spinner.Update(msg)
		cmds = append(cmds, cmd)
	}

	// Update components based on current view
	switch m.view {
	case ViewComponents:
		m.componentList, cmd = m.componentList.Update(msg)
		cmds = append(cmds, cmd)
	case ViewLogs:
		m.logViewer, cmd = m.logViewer.Update(msg)
		cmds = append(cmds, cmd)
	}

	return m, tea.Batch(cmds...)
}

func (m *Model) View() string {
	if m.width == 0 {
		return "Loading..."
	}

	switch m.view {
	case ViewDashboard:
		return m.renderDashboard()
	case ViewComponents:
		return m.renderComponents()
	case ViewOperations:
		return m.renderOperations()
	case ViewLogs:
		return m.renderLogs()
	case ViewInstall:
		return m.renderInstall()
	case ViewConfigure:
		return m.renderConfigure()
	case ViewSettings:
		return m.renderSettings()
	default:
		return "Unknown view"
	}
}

func (m *Model) loadInitialData() tea.Cmd {
	return func() tea.Msg {
		// TODO: Load data from daemon
		return nil
	}
}

func (m *Model) updateComponentSizes() {
	m.componentList.SetSize(m.width-4, m.height-10)
	m.logViewer.Width = m.width - 4
	m.logViewer.Height = m.height - 10
}

func (m *Model) nextView() {
	m.view = View((int(m.view) + 1) % 7)
}

func (m *Model) prevView() {
	if m.view == 0 {
		m.view = ViewSettings
	} else {
		m.view = View(int(m.view) - 1)
	}
}

func (m *Model) handleEnter() tea.Cmd {
	return func() tea.Msg {
		// TODO: Handle enter based on current view
		return nil
	}
}

func (m *Model) renderDashboard() string {
	return fmt.Sprintf("Dashboard View\nSize: %dx%d\nPress Tab to navigate, q to quit", m.width, m.height)
}

func (m *Model) renderComponents() string {
	return fmt.Sprintf("Components View\n%s", m.componentList.View())
}

func (m *Model) renderOperations() string {
	return "Operations View\nPress Tab to navigate, q to quit"
}

func (m *Model) renderLogs() string {
	return fmt.Sprintf("Logs View\n%s", m.logViewer.View())
}

func (m *Model) renderInstall() string {
	return "Install View\nPress Tab to navigate, q to quit"
}

func (m *Model) renderConfigure() string {
	return "Configure View\nPress Tab to navigate, q to quit"
}

func (m *Model) renderSettings() string {
	return "Settings View\nPress Tab to navigate, q to quit"
}
