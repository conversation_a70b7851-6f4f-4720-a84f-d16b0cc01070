module gitlab.thalesdigital.io/*********************/spikes/polystack

go 1.24.0

toolchain go1.24.4

require (
	// Dagger
	dagger.io/dagger v0.18.12
	github.com/charmbracelet/bubbles v0.21.0 // indirect
	github.com/charmbracelet/bubbletea v1.3.6 // indirect
	// CLI and TUI
	github.com/spf13/cobra v1.9.1

	// Testing
	github.com/stretchr/testify v1.10.0

	// Observability
	go.uber.org/zap v1.27.0

	// gRPC and API
	google.golang.org/grpc v1.73.0
)

require (
	github.com/99designs/gqlgen v0.17.76 // indirect
	github.com/Khan/genqlient v0.8.1 // indirect
	github.com/adrg/xdg v0.5.3 // indirect
	github.com/atotto/clipboard v0.1.4 // indirect
	github.com/aymanbagabas/go-osc52/v2 v2.0.1 // indirect
	github.com/cenkalti/backoff/v5 v5.0.2 // indirect
	github.com/charmbracelet/colorprofile v0.3.1 // indirect
	github.com/charmbracelet/harmonica v0.2.0 // indirect
	github.com/charmbracelet/lipgloss v1.1.0 // indirect
	github.com/charmbracelet/x/ansi v0.9.3 // indirect
	github.com/charmbracelet/x/cellbuf v0.0.13 // indirect
	github.com/charmbracelet/x/term v0.2.1 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/erikgeiser/coninput v0.0.0-20211004153227-1c3628e74d0f // indirect
	github.com/go-logr/logr v1.4.3 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect

	// Utilities
	github.com/google/uuid v1.6.0 // indirect
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.27.1 // indirect
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/lucasb-eyer/go-colorful v1.2.0 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mattn/go-localereader v0.0.1 // indirect
	github.com/mattn/go-runewidth v0.0.16 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/muesli/ansi v0.0.0-20230316100256-276c6243b2f6 // indirect
	github.com/muesli/cancelreader v0.2.2 // indirect
	github.com/muesli/termenv v0.16.0 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/rivo/uniseg v0.4.7 // indirect
	github.com/sahilm/fuzzy v0.1.1 // indirect
	github.com/sosodev/duration v1.3.1 // indirect
	github.com/spf13/pflag v1.0.6 // indirect
	github.com/vektah/gqlparser/v2 v2.5.30 // indirect
	github.com/xo/terminfo v0.0.0-20220910002029-abceb7e1c41e // indirect
	go.opentelemetry.io/auto/sdk v1.1.0 // indirect
	go.opentelemetry.io/otel v1.37.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlplog/otlploggrpc v0.13.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlplog/otlploghttp v0.13.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlpmetric/otlpmetricgrpc v1.37.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlpmetric/otlpmetrichttp v1.37.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace v1.37.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc v1.37.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp v1.37.0 // indirect
	go.opentelemetry.io/otel/log v0.13.0 // indirect
	go.opentelemetry.io/otel/metric v1.37.0 // indirect
	go.opentelemetry.io/otel/sdk v1.37.0 // indirect
	go.opentelemetry.io/otel/sdk/log v0.13.0 // indirect
	go.opentelemetry.io/otel/sdk/metric v1.37.0 // indirect
	go.opentelemetry.io/otel/trace v1.37.0 // indirect
	go.opentelemetry.io/proto/otlp v1.7.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	golang.org/x/exp v0.0.0-20250711185948-6ae5c78190dc // indirect
	golang.org/x/net v0.42.0 // indirect
	golang.org/x/sync v0.16.0 // indirect
	golang.org/x/sys v0.34.0 // indirect
	golang.org/x/text v0.27.0 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20250707201910-8d1bb00bc6a7 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20250707201910-8d1bb00bc6a7 // indirect
	google.golang.org/protobuf v1.36.6 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)

require (
	gitlab.thalesdigital.io/*********************/spikes/polystack/internal/cli v0.0.0-00010101000000-000000000000
	gitlab.thalesdigital.io/*********************/spikes/polystack/internal/config v0.0.0-00010101000000-000000000000
	gitlab.thalesdigital.io/*********************/spikes/polystack/internal/daemon v0.0.0-00010101000000-000000000000
	gitlab.thalesdigital.io/*********************/spikes/polystack/internal/tui v0.0.0-00010101000000-000000000000
)

require (
	gitlab.thalesdigital.io/*********************/spikes/polystack/internal/api v0.0.0-00010101000000-000000000000 // indirect
	gitlab.thalesdigital.io/*********************/spikes/polystack/internal/services v0.0.0-00010101000000-000000000000 // indirect
)

replace (
	gitlab.thalesdigital.io/*********************/spikes/polystack/internal/api => ./internal/api
	gitlab.thalesdigital.io/*********************/spikes/polystack/internal/cli => ./internal/cli
	gitlab.thalesdigital.io/*********************/spikes/polystack/internal/config => ./internal/config
	gitlab.thalesdigital.io/*********************/spikes/polystack/internal/daemon => ./internal/daemon
	gitlab.thalesdigital.io/*********************/spikes/polystack/internal/services => ./internal/services
	gitlab.thalesdigital.io/*********************/spikes/polystack/internal/tui => ./internal/tui
)
