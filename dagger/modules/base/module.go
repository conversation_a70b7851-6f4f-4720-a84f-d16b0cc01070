package base

import (
    "context"
    "os"
    
    "dagger.io/dagger"
    "fmt"
)

type Module struct {
    client   *dagger.Client
    platform dagger.Platform
}

func New(ctx context.Context, platform string) (*Module, error) {
    client, err := dagger.Connect(ctx, dagger.WithLogOutput(os.Stderr))
    if err != nil {
        return nil, err
    }
    
    return &Module{
        client:   client,
        platform: dagger.Platform(platform),
    }, nil
}

func (m *Module) BuildContainer(ctx context.Context, dockerfile string) (*dagger.Container, error) {
    return m.client.
        Container(dagger.ContainerOpts{Platform: m.platform}).
        Build(m.client.Host().Directory("."), dagger.BuildOpts{
            Dockerfile: dockerfile,
        }), nil
}

func (m *Module) WithPlatformOptimizations(container *dagger.Container) *dagger.Container {
    switch m.platform {
    case "linux/arm64":
        container = container.
            WithEnvVariable("GOMAXPROCS", "4").
            WithEnvVariable("GOMEMLIMIT", "7GiB")
    case "linux/amd64":
        container = container.
            WithEnvVariable("GOAMD64", "v3")
    }
    
    return container
}
