package pipeline

import (
    "context"
    "dagger.io/dagger"
    "fmt"
    "sync"
)

type MultiArchPipeline struct {
    client    *dagger.Client
    platforms []dagger.Platform
    registry  string
}

func NewMultiArchPipeline(ctx context.Context, registry string) (*MultiArchPipeline, error) {
    client, err := dagger.Connect(ctx)
    if err != nil {
        return nil, err
    }
    
    return &MultiArchPipeline{
        client: client,
        platforms: []dagger.Platform{
            "linux/amd64",
            "linux/arm64",
            "linux/arm/v7",
        },
        registry: registry,
    }, nil
}
