#!/bin/bash
set -euo pipefail

# polystack - Kubernetes Application Stack Tool
# Project Scaffold Generator
# Version: 1.0.0

PROJECT_NAME="polystack"
PROJECT_ROOT="${1:-.}"
GITHUB_ORG="${GITHUB_ORG:-yourusername}"
REGISTRY="${REGISTRY:-ghcr.io/$GITHUB_ORG}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Create directory with parents
create_dir() {
    local dir="$1"
    mkdir -p "$PROJECT_ROOT/$dir"
    log_info "Created directory: $dir"
}

# Create file with content
create_file() {
    local file="$1"
    local content="$2"
    local dir=$(dirname "$file")
    
    mkdir -p "$PROJECT_ROOT/$dir"
    echo "$content" > "$PROJECT_ROOT/$file"
    log_info "Created file: $file"
}

# Main scaffold function
scaffold_project() {
    log_info "Scaffolding polystack project in: $PROJECT_ROOT"
    
    # Create root directory
    mkdir -p "$PROJECT_ROOT"
    cd "$PROJECT_ROOT"
    
    # Create directory structure
    create_directories
    
    # Create Go module files
    create_go_modules
    
    # Create command files
    create_cmd_files
    
    # Create internal packages
    create_internal_packages
    
    # Create pkg libraries
    create_pkg_libraries
    
    # Create API definitions
    create_api_definitions
    
    # Create Dagger modules
    create_dagger_modules
    
    # Create deployment files
    create_deployment_files
    
    # Create configuration files
    create_config_files
    
    # Create build files
    create_build_files
    
    # Create test structure
    create_test_structure
    
    # Create documentation
    create_documentation
    
    # Create CI/CD pipelines
    create_cicd_pipelines
    
    # Create development tools
    create_dev_tools
    
    # Initialize git repository
    initialize_git
    
    log_success "Project scaffold completed successfully!"
    print_next_steps
}

# Create directory structure
create_directories() {
    log_info "Creating directory structure..."
    
    # Main directories
    local dirs=(
        # Commands
        "cmd/polystackd"
        "cmd/polystack"
        "cmd/polystack-installer"
        "cmd/polystack-bundle"
        
        # Internal packages
        "internal/daemon"
        "internal/api/v1"
        "internal/server/grpc"
        "internal/server/http"
        "internal/server/websocket"
        "internal/services"
        "internal/config"
        "internal/tui"
        "internal/cli"
        
        # Public packages
        "pkg/platform"
        "pkg/deployment"
        "pkg/lifecycle"
        "pkg/state"
        "pkg/certificate"
        "pkg/keyring"
        "pkg/event"
        "pkg/plugin"
        "pkg/k8s"
        "pkg/compose"
        "pkg/airgap"
        "pkg/wsl"
        "pkg/preflight"
        "pkg/postflight"
        "pkg/bundle"
        "pkg/installer"
        "pkg/utils"
        
        # Dagger modules
        "dagger/modules/base"
        "dagger/modules/postgres"
        "dagger/modules/minio"
        "dagger/modules/clickhouse"
        "dagger/modules/mlflow"
        "dagger/modules/argo"
        "dagger/modules/kserve"
        "dagger/modules/lakefs"
        "dagger/modules/zot"
        "dagger/modules/keycloak"
        "dagger/modules/certmanager"
        "dagger/modules/prometheus"
        "dagger/modules/grafana"
        "dagger/pipeline"
        
        # API definitions
        "api/proto/v1"
        "api/openapi"
        
        # Deployments
        "deployments/kubernetes/base"
        "deployments/kubernetes/overlays/dev"
        "deployments/kubernetes/overlays/staging"
        "deployments/kubernetes/overlays/production"
        "deployments/docker-compose"
        "deployments/porter"
        "deployments/helm/polystack"
        
        # Configuration
        "configs/examples"
        "configs/profiles"
        
        # Scripts
        "scripts/build"
        "scripts/test"
        "scripts/release"
        "scripts/install"
        
        # Tests
        "test/unit"
        "test/integration"
        "test/e2e"
        "test/benchmark"
        "test/fixtures"
        
        # Documentation
        "docs/api"
        "docs/guides"
        "docs/architecture"
        "docs/deployment"
        
        # CI/CD
        ".github/workflows"
        ".gitlab"
        
        # Build
        "build/docker"
        "build/package"
        
        # Hack
        "hack/tools"
        "hack/gen"
    )
    
    for dir in "${dirs[@]}"; do
        create_dir "$dir"
    done
}

# Create Go module files
create_go_modules() {
    log_info "Creating Go module files..."
    
    # Root go.mod
    create_file "go.mod" 'module github.com/'"$GITHUB_ORG"'/polystack

go 1.24

require (
    // CLI and TUI
    github.com/spf13/cobra v1.8.0
    github.com/spf13/viper v1.18.0
    github.com/charmbracelet/bubbletea v0.25.0
    github.com/charmbracelet/bubbles v0.17.1
    github.com/charmbracelet/lipgloss v0.9.1
    
    // gRPC and API
    google.golang.org/grpc v1.60.0
    google.golang.org/protobuf v1.32.0
    github.com/grpc-ecosystem/grpc-gateway/v2 v2.18.1
    github.com/grpc-ecosystem/go-grpc-middleware/v2 v2.0.1
    
    // Kubernetes
    k8s.io/api v0.29.0
    k8s.io/apimachinery v0.29.0
    k8s.io/client-go v0.29.0
    helm.sh/helm/v3 v3.13.3
    
    // Storage and State
    go.etcd.io/etcd/client/v3 v3.5.11
    github.com/lib/pq v1.10.9
    github.com/boltdb/bolt v1.3.1
    
    // Security
    github.com/hashicorp/vault/api v1.10.0
    github.com/99designs/keyring v1.2.2
    golang.org/x/crypto v0.17.0
    
    // Observability
    go.uber.org/zap v1.26.0
    github.com/prometheus/client_golang v1.18.0
    go.opentelemetry.io/otel v1.24.0
    
    // Dagger
    dagger.io/dagger v0.9.5
    
    // Testing
    github.com/stretchr/testify v1.8.4
    github.com/golang/mock v1.6.0
    
    // Utilities
    github.com/google/uuid v1.5.0
    github.com/Masterminds/semver/v3 v3.2.1
    gopkg.in/yaml.v3 v3.0.1
)'
    
    # Tools go.mod
    create_file "hack/tools/go.mod" 'module tools

go 1.24

require (
    github.com/golangci/golangci-lint v1.55.2
    google.golang.org/protobuf/cmd/protoc-gen-go v1.32.0
    google.golang.org/grpc/cmd/protoc-gen-go-grpc v1.3.0
    github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-grpc-gateway v2.18.1
    github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2 v2.18.1
    github.com/golang/mock/mockgen v1.6.0
    sigs.k8s.io/controller-tools v0.13.0
)'
}

# Create main command files
create_cmd_files() {
    log_info "Creating command files..."
    
    # polystackd daemon
    create_file "cmd/polystackd/main.go" 'package main

import (
    "context"
    "fmt"
    "os"
    "os/signal"
    "runtime"
    "syscall"
    
    "github.com/spf13/cobra"
    "github.com/'"$GITHUB_ORG"'/polystack/internal/daemon"
    "github.com/'"$GITHUB_ORG"'/polystack/internal/config"
    "go.uber.org/zap"
)

var (
    version   = "dev"
    buildTime = "unknown"
    gitCommit = "unknown"
)

func main() {
    var cfgFile string
    
    rootCmd := &cobra.Command{
        Use:   "polystackd",
        Short: "polystack Daemon - Kubernetes Application Stack Manager",
        Long: `polystack Daemon (polystackd) is the core service that manages the lifecycle
of Kubernetes applications with features including
air-gapped deployments, multi-architecture support, and comprehensive
security controls.`,
        Version: fmt.Sprintf("%s (built %s, commit %s)", version, buildTime, gitCommit),
        RunE: func(cmd *cobra.Command, args []string) error {
            return runDaemon(cfgFile)
        },
    }
    
    rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "/etc/polystack/config.yaml", "config file")
    rootCmd.AddCommand(
        newVersionCommand(),
        newHealthCommand(),
    )
    
    if err := rootCmd.Execute(); err != nil {
        fmt.Fprintln(os.Stderr, err)
        os.Exit(1)
    }
}

func runDaemon(cfgFile string) error {
    // Load configuration
    cfg, err := config.Load(cfgFile)
    if err != nil {
        return fmt.Errorf("failed to load config: %w", err)
    }
    
    // Initialize logger
    logger, err := initLogger(cfg.Log)
    if err != nil {
        return fmt.Errorf("failed to init logger: %w", err)
    }
    defer logger.Sync()
    
    // Create daemon
    d, err := daemon.New(cfg, logger)
    if err != nil {
        return fmt.Errorf("failed to create daemon: %w", err)
    }
    
    // Setup signal handling
    ctx, cancel := context.WithCancel(context.Background())
    defer cancel()
    
    sigCh := make(chan os.Signal, 1)
    signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
    
    go func() {
        <-sigCh
        logger.Info("Received shutdown signal")
        cancel()
    }()
    
    // Start daemon
    logger.Info("Starting polystack daemon",
        zap.String("version", version),
        zap.String("build_time", buildTime),
        zap.String("commit", gitCommit),
    )
    
    if err := d.Start(ctx); err != nil {
        return fmt.Errorf("daemon failed: %w", err)
    }
    
    return nil
}

func initLogger(cfg config.LogConfig) (*zap.Logger, error) {
    var zapCfg zap.Config
    
    if cfg.Format == "json" {
        zapCfg = zap.NewProductionConfig()
    } else {
        zapCfg = zap.NewDevelopmentConfig()
    }
    
    // Set log level
    if err := zapCfg.Level.UnmarshalText([]byte(cfg.Level)); err != nil {
        return nil, err
    }
    
    // Set output paths
    zapCfg.OutputPaths = []string{cfg.Output}
    if cfg.File.Enabled {
        zapCfg.OutputPaths = append(zapCfg.OutputPaths, cfg.File.Path)
    }
    
    return zapCfg.Build()
}

func newVersionCommand() *cobra.Command {
    return &cobra.Command{
        Use:   "version",
        Short: "Print version information",
        Run: func(cmd *cobra.Command, args []string) {
            fmt.Printf("polystack Daemon %s\n", version)
            fmt.Printf("Build Time: %s\n", buildTime)
            fmt.Printf("Git Commit: %s\n", gitCommit)
            fmt.Printf("Go Version: %s\n", runtime.Version())
            fmt.Printf("OS/Arch: %s/%s\n", runtime.GOOS, runtime.GOARCH)
        },
    }
}

func newHealthCommand() *cobra.Command {
    return &cobra.Command{
        Use:   "health",
        Short: "Check daemon health",
        RunE: func(cmd *cobra.Command, args []string) error {
            // Implement health check
            return nil
        },
    }
}'
    
    # polystack CLI/TUI client
    create_file "cmd/polystack/main.go" 'package main

import (
    "context"
    "fmt"
    "os"
    
    "github.com/spf13/cobra"
    "github.com/'"$GITHUB_ORG"'/polystack/internal/cli"
    "github.com/'"$GITHUB_ORG"'/polystack/internal/tui"
    "google.golang.org/grpc"
    "google.golang.org/grpc/credentials/insecure"
)

var (
    version = "dev"
)

func main() {
    var (
        daemon   string
        insecure bool
    )
    
    rootCmd := &cobra.Command{
        Use:   "polystack",
        Short: "polystack - Kubernetes Application Stack Tool",
        Long: `polystack is an tool for managing Kubernetes application
stacks with support for air-gapped deployments, multi-architecture
builds, and comprehensive lifecycle management.`,
        Version: version,
        RunE: func(cmd *cobra.Command, args []string) error {
            return runTUI(daemon, insecure)
        },
    }
    
    rootCmd.PersistentFlags().StringVar(&daemon, "daemon", "localhost:7777", "daemon address")
    rootCmd.PersistentFlags().BoolVar(&insecure, "insecure", false, "use insecure connection")
    
    // Add CLI subcommands
    rootCmd.AddCommand(
        cli.NewInstallCommand(),
        cli.NewStatusCommand(),
        cli.NewConfigureCommand(),
        cli.NewUpgradeCommand(),
        cli.NewUninstallCommand(),
        cli.NewBackupCommand(),
        cli.NewRestoreCommand(),
        cli.NewAirGapCommand(),
        cli.NewBuildCommand(),
    )
    
    if err := rootCmd.Execute(); err != nil {
        os.Exit(1)
    }
}

func runTUI(daemon string, insecureConn bool) error {
    // Connect to daemon
    opts := []grpc.DialOption{
        grpc.WithBlock(),
    }
    
    if insecureConn {
        opts = append(opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
    } else {
        // Add TLS credentials
    }
    
    conn, err := grpc.DialContext(context.Background(), daemon, opts...)
    if err != nil {
        return fmt.Errorf("failed to connect to daemon: %w", err)
    }
    defer conn.Close()
    
    // Start TUI
    return tui.Run(conn)
}'
}

# Create internal packages
create_internal_packages() {
    log_info "Creating internal packages..."
    
    # Daemon package
    create_file "internal/daemon/daemon.go" 'package daemon

import (
    "context"
    "fmt"
    "net"
    "net/http"
    "sync"
    "time"
    
    "github.com/'"$GITHUB_ORG"'/polystack/internal/config"
    "github.com/'"$GITHUB_ORG"'/polystack/internal/services"
    "go.uber.org/zap"
    "google.golang.org/grpc"
)

type Daemon struct {
    config *config.Config
    logger *zap.Logger
    
    // Core services
    stateManager     *services.StateManager
    lifecycleManager *services.LifecycleManager
    certManager      *services.CertificateManager
    keyringManager   *services.KeyringManager
    eventManager     *services.EventManager
    healthManager    *services.HealthManager
    auditManager     *services.AuditManager
    
    // Servers
    grpcServer    *grpc.Server
    httpServer    *http.Server
    metricsServer *http.Server
    
    // Lifecycle
    wg       sync.WaitGroup
    shutdown chan struct{}
}

func New(cfg *config.Config, logger *zap.Logger) (*Daemon, error) {
    d := &Daemon{
        config:   cfg,
        logger:   logger,
        shutdown: make(chan struct{}),
    }
    
    // Initialize services
    if err := d.initializeServices(); err != nil {
        return nil, fmt.Errorf("failed to initialize services: %w", err)
    }
    
    // Setup servers
    if err := d.setupServers(); err != nil {
        return nil, fmt.Errorf("failed to setup servers: %w", err)
    }
    
    return d, nil
}

func (d *Daemon) Start(ctx context.Context) error {
    d.logger.Info("Starting polystack daemon")
    
    // Start background services
    d.wg.Add(1)
    go d.runBackgroundServices(ctx)
    
    // Start servers
    if err := d.startServers(ctx); err != nil {
        return err
    }
    
    // Wait for shutdown
    <-ctx.Done()
    
    return d.shutdown()
}'
    
    # TUI model
    create_file "internal/tui/model.go" 'package tui

import (
    "context"
    "fmt"
    
    "github.com/charmbracelet/bubbles/help"
    "github.com/charmbracelet/bubbles/key"
    "github.com/charmbracelet/bubbles/list"
    "github.com/charmbracelet/bubbles/progress"
    "github.com/charmbracelet/bubbles/spinner"
    "github.com/charmbracelet/bubbles/table"
    "github.com/charmbracelet/bubbles/viewport"
    tea "github.com/charmbracelet/bubbletea"
    "github.com/charmbracelet/lipgloss"
    "github.com/'"$GITHUB_ORG"'/polystack/internal/api/v1"
    "google.golang.org/grpc"
)

type Model struct {
    client api.polystackServiceClient
    conn   *grpc.ClientConn
    
    // UI state
    view   View
    width  int
    height int
    
    // Components
    componentList  list.Model
    componentTable table.Model
    logViewer      viewport.Model
    progressBar    progress.Model
    spinner        spinner.Model
    help           help.Model
    
    // Data
    components   []*api.Component
    selectedComp *api.Component
    logs         []string
    operations   []*api.Operation
    
    // State
    loading bool
    error   error
    message string
}

type View int

const (
    ViewDashboard View = iota
    ViewComponents
    ViewOperations
    ViewLogs
    ViewInstall
    ViewConfigure
    ViewSettings
)

func NewModel(conn *grpc.ClientConn) *Model {
    m := &Model{
        client:  api.NewpolystackServiceClient(conn),
        conn:    conn,
        view:    ViewDashboard,
        spinner: spinner.New(),
        help:    help.New(),
    }
    
    return m
}

func Run(conn *grpc.ClientConn) error {
    model := NewModel(conn)
    p := tea.NewProgram(model, tea.WithAltScreen())
    
    if _, err := p.Run(); err != nil {
        return fmt.Errorf("TUI error: %w", err)
    }
    
    return nil
}'
}

# Create pkg libraries
create_pkg_libraries() {
    log_info "Creating pkg libraries..."
    
    # Platform detector
    create_file "pkg/platform/detector.go" 'package platform

import (
    "context"
    "fmt"
    "runtime"
    "strings"
    "os"
    "os/exec"
)

type Platform struct {
    OS               string
    Arch             string
    Variant          string
    IsWSL            bool
    WSLVersion       int
    IsContainer      bool
    ContainerRuntime string
    Features         []string
}

type Detector struct {
    platform *Platform
}

func NewDetector() *Detector {
    return &Detector{
        platform: &Platform{
            OS:   runtime.GOOS,
            Arch: runtime.GOARCH,
        },
    }
}

func (d *Detector) Detect(ctx context.Context) (*Platform, error) {
    // Detect architecture variant
    d.detectArchVariant()
    
    // Detect WSL
    d.detectWSL()
    
    // Detect container environment
    d.detectContainer()
    
    // Detect CPU features
    d.detectCPUFeatures()
    
    return d.platform, nil
}'
    
    # State manager interface
    create_file "pkg/state/interface.go" 'package state

import (
    "context"
    "time"
)

type Manager interface {
    // Component state operations
    GetComponentState(ctx context.Context, name string) (*ComponentState, error)
    SetComponentState(ctx context.Context, state *ComponentState) error
    ListComponents(ctx context.Context) ([]*ComponentState, error)
    DeleteComponentState(ctx context.Context, name string) error
    
    // Watch operations
    WatchComponent(ctx context.Context, name string) (<-chan StateChange, error)
    WatchAll(ctx context.Context) (<-chan StateChange, error)
    
    // Transaction support
    Transaction(ctx context.Context, fn TransactionFunc) error
    
    // Backup operations
    CreateBackup(ctx context.Context, description string) (*Backup, error)
    RestoreBackup(ctx context.Context, backupID string) error
    ListBackups(ctx context.Context) ([]*Backup, error)
}

type ComponentState struct {
    Name            string                 `json:"name"`
    Version         string                 `json:"version"`
    Status          ComponentStatus        `json:"status"`
    InstalledAt     time.Time             `json:"installed_at"`
    LastConfigured  time.Time             `json:"last_configured"`
    Configuration   map[string]interface{} `json:"configuration"`
    Credentials     map[string]string      `json:"credentials,omitempty"`
    Endpoints       map[string]string      `json:"endpoints"`
    Dependencies    []string               `json:"dependencies"`
    CertificateRefs []string               `json:"certificate_refs"`
}

type ComponentStatus string

const (
    StatusNotInstalled ComponentStatus = "not_installed"
    StatusInstalling   ComponentStatus = "installing"
    StatusInstalled    ComponentStatus = "installed"
    StatusConfiguring  ComponentStatus = "configuring"
    StatusReady        ComponentStatus = "ready"
    StatusError        ComponentStatus = "error"
    StatusUninstalling ComponentStatus = "uninstalling"
)'
}

# Create API definitions
create_api_definitions() {
    log_info "Creating API definitions..."
    
    # Proto file
    create_file "api/proto/v1/polystack.proto" 'syntax = "proto3";

package polystack.api.v1;

option go_package = "github.com/'"$GITHUB_ORG"'/polystack/internal/api/v1;api";

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

service polystackService {
  // Component operations
  rpc ListComponents(ListComponentsRequest) returns (ListComponentsResponse) {
    option (google.api.http) = {
      get: "/v1/components"
    };
  }
  
  rpc GetComponent(GetComponentRequest) returns (Component) {
    option (google.api.http) = {
      get: "/v1/components/{name}"
    };
  }
  
  rpc InstallComponent(InstallComponentRequest) returns (stream InstallComponentResponse) {
    option (google.api.http) = {
      post: "/v1/components/{component}/install"
      body: "*"
    };
  }
  
  rpc ConfigureComponent(ConfigureComponentRequest) returns (stream ConfigureComponentResponse) {
    option (google.api.http) = {
      post: "/v1/components/{component}/configure"
      body: "*"
    };
  }
  
  rpc UninstallComponent(UninstallComponentRequest) returns (stream UninstallComponentResponse) {
    option (google.api.http) = {
      delete: "/v1/components/{component}"
    };
  }
  
  // Operations
  rpc ListOperations(ListOperationsRequest) returns (ListOperationsResponse) {
    option (google.api.http) = {
      get: "/v1/operations"
    };
  }
  
  rpc GetOperation(GetOperationRequest) returns (Operation) {
    option (google.api.http) = {
      get: "/v1/operations/{id}"
    };
  }
  
  // Logs
  rpc StreamLogs(StreamLogsRequest) returns (stream LogEntry) {
    option (google.api.http) = {
      get: "/v1/logs/stream"
    };
  }
  
  // Health
  rpc Health(google.protobuf.Empty) returns (HealthResponse) {
    option (google.api.http) = {
      get: "/v1/health"
    };
  }
}

message Component {
  string name = 1;
  string version = 2;
  string status = 3;
  google.protobuf.Timestamp installed_at = 4;
  map<string, string> endpoints = 5;
  ComponentHealth health = 6;
}

message ComponentHealth {
  bool healthy = 1;
  string message = 2;
  google.protobuf.Timestamp last_check = 3;
}'
}

# Create Dagger modules
create_dagger_modules() {
    log_info "Creating Dagger modules..."
    
    # Base module
    create_file "dagger/modules/base/module.go" 'package base

import (
    "context"
    "os"
    
    "dagger.io/dagger"
    "fmt"
)

type Module struct {
    client   *dagger.Client
    platform dagger.Platform
}

func New(ctx context.Context, platform string) (*Module, error) {
    client, err := dagger.Connect(ctx, dagger.WithLogOutput(os.Stderr))
    if err != nil {
        return nil, err
    }
    
    return &Module{
        client:   client,
        platform: dagger.Platform(platform),
    }, nil
}

func (m *Module) BuildContainer(ctx context.Context, dockerfile string) (*dagger.Container, error) {
    return m.client.
        Container(dagger.ContainerOpts{Platform: m.platform}).
        Build(m.client.Host().Directory("."), dagger.BuildOpts{
            Dockerfile: dockerfile,
        }), nil
}

func (m *Module) WithPlatformOptimizations(container *dagger.Container) *dagger.Container {
    switch m.platform {
    case "linux/arm64":
        container = container.
            WithEnvVariable("GOMAXPROCS", "4").
            WithEnvVariable("GOMEMLIMIT", "7GiB")
    case "linux/amd64":
        container = container.
            WithEnvVariable("GOAMD64", "v3")
    }
    
    return container
}'
    
    # Multi-arch pipeline
    create_file "dagger/pipeline/multiarch.go" 'package pipeline

import (
    "context"
    "dagger.io/dagger"
    "fmt"
    "sync"
)

type MultiArchPipeline struct {
    client    *dagger.Client
    platforms []dagger.Platform
    registry  string
}

func NewMultiArchPipeline(ctx context.Context, registry string) (*MultiArchPipeline, error) {
    client, err := dagger.Connect(ctx)
    if err != nil {
        return nil, err
    }
    
    return &MultiArchPipeline{
        client: client,
        platforms: []dagger.Platform{
            "linux/amd64",
            "linux/arm64",
            "linux/arm/v7",
        },
        registry: registry,
    }, nil
}'
}

# Create deployment files
create_deployment_files() {
    log_info "Creating deployment files..."
    
    # Docker Compose
    create_file "deployments/docker-compose/docker-compose.yml" 'version: "3.9"

services:
  polystackd:
    image: ${REGISTRY}/polystack/polystackd:${VERSION:-latest}
    container_name: polystackd
    hostname: polystackd
    ports:
      - "7777:7777"  # gRPC
      - "8080:8080"  # HTTP
      - "9090:9090"  # Metrics
    volumes:
      - polystackd-data:/var/lib/polystack
      - ./config:/etc/polystack
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      polystack_MODE: docker-compose
      polystack_LOG_LEVEL: ${LOG_LEVEL:-info}
    networks:
      - polystack
    restart: unless-stopped
    depends_on:
      etcd:
        condition: service_healthy
      postgres:
        condition: service_healthy
      vault:
        condition: service_started

  etcd:
    image: quay.io/coreos/etcd:v3.5.11
    container_name: etcd
    hostname: etcd
    environment:
      ETCD_NAME: etcd0
      ETCD_DATA_DIR: /etcd-data
      ETCD_LISTEN_CLIENT_URLS: http://0.0.0.0:2379
      ETCD_ADVERTISE_CLIENT_URLS: http://etcd:2379
      ETCD_LISTEN_PEER_URLS: http://0.0.0.0:2380
      ETCD_INITIAL_ADVERTISE_PEER_URLS: http://etcd:2380
      ETCD_INITIAL_CLUSTER: etcd0=http://etcd:2380
      ETCD_INITIAL_CLUSTER_TOKEN: polystack-etcd-cluster
      ETCD_INITIAL_CLUSTER_STATE: new
    volumes:
      - etcd-data:/etcd-data
    networks:
      - polystack
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 10s
      timeout: 5s
      retries: 5

  postgres:
    image: postgres:15-alpine
    container_name: postgres
    hostname: postgres
    environment:
      POSTGRES_USER: polystack
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: polystack
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - polystack
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U polystack"]
      interval: 10s
      timeout: 5s
      retries: 5

  vault:
    image: hashicorp/vault:latest
    container_name: vault
    hostname: vault
    cap_add:
      - IPC_LOCK
    environment:
      VAULT_DEV_ROOT_TOKEN_ID: ${VAULT_TOKEN}
      VAULT_DEV_LISTEN_ADDRESS: 0.0.0.0:8200
    networks:
      - polystack

networks:
  polystack:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  polystackd-data:
  etcd-data:
  postgres-data:'
    
    # Kubernetes base
    create_file "deployments/kubernetes/base/kustomization.yaml" 'apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: polystack-system

resources:
  - namespace.yaml
  - serviceaccount.yaml
  - rbac.yaml
  - configmap.yaml
  - secret.yaml
  - deployment.yaml
  - service.yaml
  - ingress.yaml

configMapGenerator:
  - name: polystack-config
    files:
      - config.yaml

secretGenerator:
  - name: polystack-secrets
    literals:
      - encryption-key=${polystack_ENCRYPTION_KEY}
      - vault-token=${VAULT_TOKEN}

images:
  - name: polystackd
    newName: ghcr.io/'"$GITHUB_ORG"'/polystack/polystackd
    newTag: latest'
    
    # Porter bundle
    create_file "deployments/porter/porter.yaml" 'schemaVersion: 1.0.0-alpha.2
name: polystack
version: 0.1.0
description: polystack - Kubernetes Application Stack Tool
registry: ${REGISTRY}

credentials:
  - name: kubeconfig
    description: Kubernetes configuration file
    path: /home/<USER>/.kube/config
  
  - name: docker-config
    description: Docker configuration
    path: /home/<USER>/.docker/config.json

parameters:
  - name: installation-mode
    description: Installation mode (kubernetes, docker-compose, airgap)
    type: string
    default: kubernetes
    
  - name: deployment-profile
    description: Deployment profile (minimal, standard, full)
    type: string
    default: standard
    
  - name: platform
    description: Target platform
    type: string
    default: linux/amd64

outputs:
  - name: daemon-endpoint
    description: polystack daemon endpoint
    type: string
    
  - name: admin-password
    description: Admin password
    type: string
    sensitive: true

mixins:
  - exec
  - kubernetes

install:
  - exec:
      description: "Run preflight checks"
      command: /cnab/app/polystack-installer
      arguments:
        - preflight
        - --mode
        - ${bundle.parameters.installation-mode}
        
  - exec:
      description: "Install polystack"
      command: /cnab/app/polystack-installer
      arguments:
        - install
        - --mode
        - ${bundle.parameters.installation-mode}
        - --profile
        - ${bundle.parameters.deployment-profile}
        
  - exec:
      description: "Run postflight validation"
      command: /cnab/app/polystack-installer
      arguments:
        - postflight

upgrade:
  - exec:
      description: "Upgrade polystack"
      command: /cnab/app/polystack-installer
      arguments:
        - upgrade

uninstall:
  - exec:
      description: "Uninstall polystack"
      command: /cnab/app/polystack-installer
      arguments:
        - uninstall'
}

# Create configuration files
create_config_files() {
    log_info "Creating configuration files..."
    
    # Example config
    create_file "configs/examples/config.yaml" '# polystack Configuration
daemon:
  pid_file: /var/run/polystack/polystackd.pid
  working_dir: /var/lib/polystack

grpc:
  address: ":7777"
  max_recv_msg_size: 10485760
  max_send_msg_size: 10485760

http:
  address: ":8080"
  read_timeout: 30s
  write_timeout: 30s

metrics:
  address: ":9090"
  path: /metrics

log:
  level: info
  format: json
  output: stdout

security:
  tls:
    enabled: true
    cert_file: /etc/polystack/certs/server.crt
    key_file: /etc/polystack/certs/server.key
    ca_file: /etc/polystack/certs/ca.crt
  
  encryption:
    enabled: true
    algorithm: aes-256-gcm

storage:
  backend: etcd
  etcd:
    endpoints:
      - http://localhost:2379
    dial_timeout: 5s
    
  postgres:
    dsn: postgres://polystack:password@localhost/polystack?sslmode=disable
    
components:
  registry: https://registry.polystack.io
  cache_dir: /var/cache/polystack/components'
    
    # Profile configurations
    create_file "configs/profiles/minimal.yaml" 'name: minimal
description: Minimal installation profile
components:
  - cert-manager
  - minio
  - postgresql'
    
    create_file "configs/profiles/standard.yaml" 'name: standard
description: Standard installation profile
components:
  - cert-manager
  - keycloak
  - minio
  - postgresql
  - clickhouse
  - argo
  - mlflow'
    
    create_file "configs/profiles/full.yaml" 'name: full
description: Full installation profile with all components
components:
  - cert-manager
  - keycloak
  - prometheus
  - grafana
  - minio
  - postgresql
  - clickhouse
  - argo
  - mlflow
  - kserve
  - lakefs
  - zot
  - dagger
  - polycore'
}

# Create build files
create_build_files() {
    log_info "Creating build files..."
    
    # Makefile
    create_file "Makefile" '# polystack Makefile
SHELL := /bin/bash

# Variables
REGISTRY ?= ghcr.io/$(GITHUB_ORG)
VERSION ?= $(shell git describe --tags --always --dirty)
PLATFORMS ?= linux/amd64,linux/arm64
GOARCH ?= $(shell go env GOARCH)
GOOS ?= $(shell go env GOOS)

# Go parameters
GOCMD = go
GOBUILD = $(GOCMD) build
GOTEST = $(GOCMD) test
GOMOD = $(GOCMD) mod
GOVET = $(GOCMD) vet
GOFMT = gofmt

# Directories
CMD_DIR = ./cmd
PKG_DIR = ./pkg
INTERNAL_DIR = ./internal
BUILD_DIR = ./build
DIST_DIR = ./dist

# Binaries
polystackD_BINARY = polystackd
polystack_BINARY = polystack
INSTALLER_BINARY = polystack-installer

# Targets
.PHONY: all build test clean

all: test build

## Build targets
build: build-polystackd build-polystack build-installer

build-polystackd:
	@echo "Building polystackd..."
	@CGO_ENABLED=0 $(GOBUILD) -ldflags "-X main.version=$(VERSION)" -o $(DIST_DIR)/$(polystackD_BINARY) $(CMD_DIR)/polystackd

build-polystack:
	@echo "Building polystack..."
	@CGO_ENABLED=0 $(GOBUILD) -ldflags "-X main.version=$(VERSION)" -o $(DIST_DIR)/$(polystack_BINARY) $(CMD_DIR)/polystack

build-installer:
	@echo "Building polystack-installer..."
	@CGO_ENABLED=0 $(GOBUILD) -ldflags "-X main.version=$(VERSION)" -o $(DIST_DIR)/$(INSTALLER_BINARY) $(CMD_DIR)/polystack-installer

## Test targets
test:
	@echo "Running tests..."
	@$(GOTEST) -v -race -coverprofile=coverage.out ./...

test-unit:
	@echo "Running unit tests..."
	@$(GOTEST) -v -short ./...

test-integration:
	@echo "Running integration tests..."
	@$(GOTEST) -v -tags=integration ./test/integration/...

test-e2e:
	@echo "Running e2e tests..."
	@$(GOTEST) -v -tags=e2e ./test/e2e/...

## Code quality
fmt:
	@echo "Formatting code..."
	@$(GOFMT) -s -w .

vet:
	@echo "Running go vet..."
	@$(GOVET) ./...

lint:
	@echo "Running golangci-lint..."
	@golangci-lint run

## Container targets
docker-build:
	@echo "Building Docker images..."
	@docker buildx build --platform $(PLATFORMS) -t $(REGISTRY)/polystackd:$(VERSION) -f build/docker/Dockerfile.polystackd .
	@docker buildx build --platform $(PLATFORMS) -t $(REGISTRY)/polystack:$(VERSION) -f build/docker/Dockerfile.polystack .

docker-push:
	@echo "Pushing Docker images..."
	@docker buildx build --push --platform $(PLATFORMS) -t $(REGISTRY)/polystackd:$(VERSION) -f build/docker/Dockerfile.polystackd .
	@docker buildx build --push --platform $(PLATFORMS) -t $(REGISTRY)/polystack:$(VERSION) -f build/docker/Dockerfile.polystack .

## Dagger targets
dagger-build:
	@echo "Building with Dagger..."
	@go run ./dagger/cmd/build

## Proto generation
proto:
	@echo "Generating protobuf files..."
	@buf generate

## Dependencies
deps:
	@echo "Downloading dependencies..."
	@$(GOMOD) download

tidy:
	@echo "Tidying dependencies..."
	@$(GOMOD) tidy

## Clean
clean:
	@echo "Cleaning..."
	@rm -rf $(DIST_DIR) $(BUILD_DIR) coverage.out

## Help
help:
	@echo "Available targets:"
	@grep -E "^[a-zA-Z_-]+:.*?## .*$$" $(MAKEFILE_LIST) | awk '\''BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'\''

.DEFAULT_GOAL := help'
    
    # Dockerfiles
    create_file "build/docker/Dockerfile.polystackd" '# Build stage
FROM golang:1.24-alpine AS builder

RUN apk add --no-cache git make

WORKDIR /build
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN make build-polystackd

# Runtime stage
FROM alpine:3.18

RUN apk add --no-cache ca-certificates tzdata

COPY --from=builder /build/dist/polystackd /usr/local/bin/polystackd

RUN adduser -D -u 1000 polystack && \
    mkdir -p /var/lib/polystack /etc/polystack && \
    chown -R polystack:polystack /var/lib/polystack /etc/polystack

USER polystack

EXPOSE 7777 8080 9090

ENTRYPOINT ["/usr/local/bin/polystackd"]'
    
    create_file "build/docker/Dockerfile.polystack" '# Build stage
FROM golang:1.24-alpine AS builder

RUN apk add --no-cache git make

WORKDIR /build
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN make build-polystack

# Runtime stage
FROM alpine:3.18

RUN apk add --no-cache ca-certificates

COPY --from=builder /build/dist/polystack /usr/local/bin/polystack

ENTRYPOINT ["/usr/local/bin/polystack"]'
}

# Create test structure
create_test_structure() {
    log_info "Creating test structure..."
    
    # Unit test example
    create_file "pkg/platform/detector_test.go" 'package platform

import (
    "context"
    "testing"
    
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"
)

func TestDetector_Detect(t *testing.T) {
    tests := []struct {
        name     string
        setup    func()
        wantArch string
        wantErr  bool
    }{
        {
            name: "detect amd64",
            setup: func() {
                // Mock setup
            },
            wantArch: "amd64",
            wantErr:  false,
        },
        {
            name: "detect arm64",
            setup: func() {
                // Mock setup
            },
            wantArch: "arm64",
            wantErr:  false,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            if tt.setup != nil {
                tt.setup()
            }
            
            d := NewDetector()
            got, err := d.Detect(context.Background())
            
            if tt.wantErr {
                require.Error(t, err)
                return
            }
            
            require.NoError(t, err)
            assert.Equal(t, tt.wantArch, got.Arch)
        })
    }
}'
    
    # Integration test example
    create_file "test/integration/daemon_test.go" '// +build integration

package integration

import (
    "context"
    "testing"
    "time"
    
    "github.com/stretchr/testify/require"
    "google.golang.org/grpc"
    "google.golang.org/grpc/credentials/insecure"
)

func TestDaemonStartup(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping integration test")
    }
    
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()
    
    // Start daemon
    // ...
    
    // Connect to daemon
    conn, err := grpc.DialContext(ctx, "localhost:7777",
        grpc.WithTransportCredentials(insecure.NewCredentials()),
        grpc.WithBlock(),
    )
    require.NoError(t, err)
    defer conn.Close()
    
    // Test health endpoint
    // ...
}'
}

# Create documentation
create_documentation() {
    log_info "Creating documentation..."
    
    # README
    create_file "README.md" '# polystack - Kubernetes Application Stack Tool

[![Build Status](https://github.com/'"$GITHUB_ORG"'/polystack/workflows/CI/badge.svg)](https://github.com/'"$GITHUB_ORG"'/polystack/actions)
[![Go Report Card](https://goreportcard.com/badge/github.com/'"$GITHUB_ORG"'/polystack)](https://goreportcard.com/report/github.com/'"$GITHUB_ORG"'/polystack)
[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](LICENSE)

polystack is an tool for managing Kubernetes application stacks with support for air-gapped deployments, multi-architecture builds, and comprehensive lifecycle management.

## Features

- 🚀 **Multi-Platform Support**: Kubernetes, Docker Compose, and bare metal deployments
- 🔒 **Air-Gapped Deployments**: Full offline installation support
- 🏗️ **Multi-Architecture**: AMD64, ARM64, and ARMv7 support
- 🎨 **Modern TUI**: Interactive terminal interface with real-time updates
- 🔐 **Security**: mTLS, certificate auto-rotation, and HashiCorp Vault integration
- 📊 **Observability**: Built-in metrics, logging, and tracing
- 🔄 **Lifecycle Management**: Install, configure, upgrade, and uninstall with zero downtime
- 🌍 **WSL Support**: Native Windows Subsystem for Linux integration

## Quick Start

### Installation

```bash
# Install polystack CLI
curl -sSL https://get.polystack.io | bash

# Start the daemon
sudo systemctl start polystackd

# Launch the TUI
polystack
```

### Docker Compose

```bash
# Clone the repository
git clone https://github.com/'"$GITHUB_ORG"'/polystack.git
cd polystack

# Start with Docker Compose
docker-compose up -d

# Connect with CLI
polystack --daemon localhost:7777
```

### Air-Gapped Installation

```bash
# Create bundle on internet-connected machine
polystack airgap create-bundle -o polystack-bundle.tar.gz

# Transfer bundle to air-gapped environment
# Then install
polystack airgap install-bundle -b polystack-bundle.tar.gz
```

## Architecture

polystack follows a client-server architecture with a central daemon (`polystackd`) managing all operations:

- **polystackd**: Core daemon providing gRPC and REST APIs
- **polystack**: CLI and TUI client
- **State Management**: Distributed state with etcd and PostgreSQL
- **Security**: Certificate management with auto-rotation
- **Deployment Engines**: Pluggable engines for different platforms

## Documentation

- [Getting Started](docs/guides/getting-started.md)
- [Architecture Overview](docs/architecture/overview.md)
- [API Reference](docs/api/reference.md)
- [Deployment Guide](docs/deployment/guide.md)

## Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## License

polystack is licensed under the [Apache License 2.0](LICENSE).'
    
    # Architecture doc
    create_file "docs/architecture/overview.md" '# polystack Architecture Overview

## System Architecture

polystack is designed as a distributed, system for managing Kubernetes application stacks.

### Core Components

1. **polystack Daemon (polystackd)**
   - Central control plane
   - gRPC and REST API servers
   - State management
   - Event bus
   - Plugin system

2. **Client Layer**
   - CLI (`polystack`)
   - TUI (Terminal User Interface)
   - SDKs (Go, Python, JavaScript)

3. **Deployment Engines**
   - Kubernetes Engine
   - Docker Compose Engine
   - Air-Gap Engine
   - Bare Metal Engine

4. **Storage Backends**
   - etcd for distributed configuration
   - PostgreSQL for relational data
   - HashiCorp Vault for secrets
   - Local filesystem for cache

### Key Features

#### Multi-Architecture Support
- AMD64, ARM64, ARMv7
- Platform-specific optimizations
- Cross-compilation support

#### Security
- mTLS for all communications
- Certificate auto-rotation
- RBAC with policy enforcement
- Audit logging

#### High Availability
- Multi-master support
- State replication
- Automatic failover
- Self-healing

### Deployment Modes

1. **Kubernetes Mode**
   - Native Kubernetes integration
   - Helm chart support
   - Operator pattern

2. **Docker Compose Mode**
   - Simplified deployment
   - Single-node or multi-node
   - Persistent volumes

3. **Air-Gapped Mode**
   - Offline installation
   - Bundle creation
   - Local registry support

### API Design

polystack provides three API interfaces:

1. **gRPC API** (port 7777)
   - Primary API for clients
   - Streaming support
   - Efficient binary protocol

2. **REST API** (port 8080)
   - HTTP/JSON gateway
   - OpenAPI specification
   - Web-friendly

3. **WebSocket API** (port 8081)
   - Real-time updates
   - Event streaming
   - Bidirectional communication'
}

# # Create CI/CD pipelines
# create_cicd_pipelines() {
#     log_info "Creating CI/CD pipelines..."
    
#     # GitHub Actions
#     create_file ".github/workflows/ci.yml" 'name: CI

# on:
#   push:
#     branches: [ main, develop ]
#   pull_request:
#     branches: [ main ]

# env:
#   GO_VERSION: "1.24"
#   REGISTRY: ghcr.io

# jobs:
#   lint:
#     name: Lint
#     runs-on: ubuntu-latest
#     steps:
#       - uses: actions/checkout@v4
      
#       - uses: actions/setup-go@v5
#         with:
#           go-version: ${{ env.GO_VERSION }}
          
#       - name: Run golangci-lint
#         uses: golangci/golangci-lint-action@v3
#         with:
#           version: latest
          
#   test:
#     name: Test
#     runs-on: ubuntu-latest
#     strategy:
#       matrix:
#         arch: [amd64, arm64]
#     steps:
#       - uses: actions/checkout@v4
      
#       - uses: actions/setup-go@v5
#         with:
#           go-version: ${{ env.GO_VERSION }}
          
#       - name: Run tests
#         run: |
#           make test
          
#       - name: Upload coverage
#         uses: codecov/codecov-action@v3
#         with:
#           file: ./coverage.out
          
#   build:
#     name: Build
#     runs-on: ubuntu-latest
#     needs: [lint, test]
#     strategy:
#       matrix:
#         platform: [linux/amd64, linux/arm64, linux/arm/v7]
#     steps:
#       - uses: actions/checkout@v4
      
#       - uses: docker/setup-qemu-action@v3
      
#       - uses: docker/setup-buildx-action@v3
      
#       - name: Log in to registry
#         uses: docker/login-action@v3
#         with:
#           registry: ${{ env.REGISTRY }}
#           username: ${{ github.actor }}
#           password: ${{ secrets.GITHUB_TOKEN }}
          
#       - name: Build and push
#         uses: docker/build-push-action@v5
#         with:
#           context: .
#           platforms: ${{ matrix.platform }}
#           push: true
#           tags: |
#             ${{ env.REGISTRY }}/${{ github.repository }}:latest
#             ${{ env.REGISTRY }}/${{ github.repository }}:${{ github.sha }}'
    
#     # GitLab CI
#     create_file ".gitlab-ci.yml" 'stages:
#   - lint
#   - test
#   - build
#   - deploy

# variables:
#   GO_VERSION: "1.24"
#   DOCKER_DRIVER: overlay2

# before_script:
#   - apt-get update -qq && apt-get install -y -qq git
#   - go version

# lint:
#   stage: lint
#   image: golang:${GO_VERSION}
#   script:
#     - make lint
    
# test:
#   stage: test
#   image: golang:${GO_VERSION}
#   script:
#     - make test
#   coverage: \'/coverage: \d+\.\d+%/\'
    
# build:
#   stage: build
#   image: docker:latest
#   services:
#     - docker:dind
#   script:
#     - docker buildx create --use
#     - make docker-build'

# # Create development tools
# create_dev_tools() {
#     log_info "Creating development tools..."
    
#     # VS Code settings
#     mkdir -p "$PROJECT_ROOT/.vscode"
#     cat > "$PROJECT_ROOT/.vscode/settings.json" << 'EOF'
# {
#     "go.useLanguageServer": true,
#     "go.lintTool": "golangci-lint",
#     "go.lintFlags": [
#         "--fast"
#     ],
#     "go.testFlags": ["-v"],
#     "go.testTimeout": "10m",
#     "go.coverOnSave": true,
#     "go.coverageDecorator": {
#         "type": "gutter",
#         "coveredHighlightColor": "rgba(64,128,64,0.5)",
#         "uncoveredHighlightColor": "rgba(128,64,64,0.5)",
#         "coveredGutterStyle": "blockgreen",
#         "uncoveredGutterStyle": "blockred"
#     },
#     "files.exclude": {
#         "**/.git": true,
#         "**/.DS_Store": true,
#         "**/node_modules": true,
#         "dist": true,
#         "build": true
#     }
# }
# EOF
    
#     # Dev container
#     mkdir -p "$PROJECT_ROOT/.devcontainer"
#     cat > "$PROJECT_ROOT/.devcontainer/devcontainer.json" << 'EOF'
# {
#     "name": "polystack Development",
#     "dockerComposeFile": "../deployments/docker-compose/docker-compose.dev.yml",
#     "service": "devcontainer",
#     "workspaceFolder": "/workspace",
#     "features": {
#         "ghcr.io/devcontainers/features/go:1": {
#             "version": "1.24"
#         },
#         "ghcr.io/devcontainers/features/docker-in-docker:2": {},
#         "ghcr.io/devcontainers/features/kubectl-helm-minikube:1": {}
#     },
#     "customizations": {
#         "vscode": {
#             "extensions": [
#                 "golang.go",
#                 "ms-kubernetes-tools.vscode-kubernetes-tools",
#                 "ms-azuretools.vscode-docker",
#                 "redhat.vscode-yaml"
#             ]
#         }
#     },
#     "postCreateCommand": "make deps",
#     "remoteUser": "vscode"
# }
# EOF
    
#     # Pre-commit hooks
#     create_file ".pre-commit-config.yaml" 'repos:
#   - repo: https://github.com/pre-commit/pre-commit-hooks
#     rev: v4.5.0
#     hooks:
#       - id: trailing-whitespace
#       - id: end-of-file-fixer
#       - id: check-yaml
#       - id: check-added-large-files
      
#   - repo: https://github.com/dnephin/pre-commit-golang
#     rev: v0.5.1
#     hooks:
#       - id: go-fmt
#       - id: go-vet
#       - id: go-imports
#       - id: go-cyclo
#         args: [-over=15]
#       - id: go-mod-tidy
      
#   - repo: https://github.com/golangci/golangci-lint
#     rev: v1.55.2
#     hooks:
#       - id: golangci-lint'
# }

# # Initialize git repository
# initialize_git() {
#     log_info "Initializing git repository..."
    
#     # .gitignore
#     create_file ".gitignore" '# Binaries
# *.exe
# *.exe~
# *.dll
# *.so
# *.dylib
# /dist/
# /build/

# # Test binary, built with go test -c
# *.test

# # Output of the go coverage tool
# *.out
# coverage.html

# # Dependency directories
# vendor/

# # Go workspace file
# go.work

# # IDE
# .idea/
# .vscode/
# *.swp
# *.swo
# *~

# # OS
# .DS_Store
# Thumbs.db

# # Environment
# .env
# .env.local
# *.local

# # Logs
# *.log

# # Temporary files
# *.tmp
# *.temp

# # polystack specific
# /data/
# /certs/
# *.tar.gz
# *.zip

# # Dagger
# /dagger.json
# /.dagger/

# # Porter
# /.cnab/
# /bundle.json'
    
#     # Initialize git
#     if [ ! -d ".git" ]; then
#         git init
#         git add .
#         git commit -m "Initial commit: polystack project scaffold"
#     fi
# }

# Print next steps
print_next_steps() {
    echo
    echo "════════════════════════════════════════════════════════════════"
    log_success "polystack project scaffold created successfully!"
    echo "════════════════════════════════════════════════════════════════"
    echo
    echo "Next steps:"
    echo "1. cd $PROJECT_ROOT"
    echo "2. Update go.mod with your organization: sed -i \"s/\$GITHUB_ORG/your-org/g\" go.mod"
    echo "3. Run \"make deps\" to download dependencies"
    echo "4. Run \"make proto\" to generate protobuf files"
    echo "5. Run \"make build\" to build the project"
    echo "6. Run \"make test\" to run tests"
    echo
    echo "To start development:"
    echo "- Docker Compose: docker-compose -f deployments/docker-compose/docker-compose.yml up"
    echo "- VS Code: code . (with Dev Container support)"
    echo "- Build multi-arch: make docker-build PLATFORMS=linux/amd64,linux/arm64"
    echo
    echo "Documentation:"
    echo "- Architecture: docs/architecture/overview.md"
    echo "- Getting Started: docs/guides/getting-started.md"
    echo "- API Reference: docs/api/reference.md"
    echo
    log_info "Happy coding! 🚀"
}

# Main execution
main() {
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              polystack Project Scaffold Generator                 ║"
    echo "║                    Version 1.0.0                             ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo
    
    scaffold_project
}

# Run main function
main
