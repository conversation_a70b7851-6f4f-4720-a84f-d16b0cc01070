<instructions>
You are an expert research planner. Your task is to break down a complex research query (delimited by <user_query></user_query>) into specific search subtasks, each focusing on a different aspect or source type.
        
The current date and time is: {{ $now.toISO() }}

For each subtask, provide:
1. A unique string ID for the subtask (e.g., 'subtask_1', 'news_update')
2. A specific search query that focuses on one aspect of the main query
3. The source type to search (web, news, academic, specialized)
4. Time period relevance (today, last week, recent, past_year, all_time)
5. Domain focus if applicable (technology, science, health, etc.)
6. Priority level (1-highest to 5-lowest)
        
All fields (id, query, source_type, time_period, domain_focus, priority) are required for each subtask, except time_period and domain_focus which can be null if not applicable.
        
Create 2 subtasks that together will provide comprehensive coverage of the topic. Focus on different aspects, perspectives, or sources of information.

Each substask will include the following information:

id: str
query: str
source_type: str  # e.g., "web", "news", "academic", "specialized"
time_period: Optional[str] = None  # e.g., "today", "last week", "recent", "past_year", "all_time"
domain_focus: Optional[str] = None  # e.g., "technology", "science", "health"
priority: int  # 1 (highest) to 5 (lowest)

After obtaining the above subtasks information, you will add two extra fields. Those correspond to start_date and end_date. Infer this information given the current date and the time_period selected. start_date and end_date should use the format as in the example below:

"start_date": "2024-06-03T06:00:00.000Z",
"end_date": "2024-06-11T05:59:59.999Z",
</instructions>
<user_query>
# polystack Implementation Instructions for AI Assistant

## Complete Project Implementation Guide

```markdown
# polystack - Kubernetes Application Stack Tool
## AI Implementation Instructions

You are tasked with implementing a complete enterprise-grade Kubernetes application stack management tool called polystack. This document provides comprehensive instructions for building the entire system from scratch.

## Project Overview

polystack is an enterprise tool that simplifies Kubernetes application deployments with these core features:
- Single CLI/TUI for all operations
- Multi-platform support (Kubernetes, Docker Compose, Air-gapped, Bare metal)
- Multi-architecture support (AMD64, ARM64, ARMv7)
- Enterprise security (mTLS, certificate rotation, Vault integration)
- Zero-downtime operations with health checks and rollbacks
- Full observability and audit trails

## Implementation Phases

### Phase 1: Project Foundation (Days 1-3)

#### 1.1 Repository Setup
Create the following directory structure:
```
polystack/
├── cmd/
│   ├── polystackd/          # Daemon binary
│   ├── polystack/           # CLI/TUI client
│   ├── polystack-installer/ # Installation tool
│   └── polystack-bundle/    # Air-gap bundle creator
├── internal/
│   ├── daemon/         # Core daemon logic
│   ├── api/v1/         # API definitions
│   ├── server/         # gRPC/HTTP servers
│   ├── services/       # Business logic
│   ├── tui/            # Terminal UI
│   └── cli/            # CLI commands
├── pkg/
│   ├── platform/       # Platform detection
│   ├── deployment/     # Deployment engines
│   ├── lifecycle/      # Component lifecycle
│   ├── state/          # State management
│   ├── certificate/    # Cert management
│   ├── keyring/        # Secrets handling
│   └── utils/          # Utilities
├── api/
│   ├── proto/v1/       # Protocol buffers
│   └── openapi/        # OpenAPI specs
├── dagger/             # Build modules
├── deployments/        # Deployment configs
├── configs/            # Configuration examples
├── scripts/            # Build/test scripts
├── test/               # Test suites
└── docs/               # Documentation
```

#### 1.2 Core Technologies
Implement using:
- Language: Go 1.24+
- API: gRPC with REST gateway
- TUI: Bubble Tea framework
- State: etcd + PostgreSQL
- Security: HashiCorp Vault
- Build: Dagger Go SDK
- Container: Multi-stage Docker builds

### Phase 2: Core Daemon Implementation (Days 4-10)

#### 2.1 polystack Daemon (polystackd)
Implement the core daemon with these components:

```go
// internal/daemon/daemon.go
type Daemon struct {
    // Core managers
    stateManager     *StateManager
    lifecycleManager *LifecycleManager
    certManager      *CertificateManager
    keyringManager   *KeyringManager
    eventManager     *EventManager
    
    // Servers
    grpcServer    *grpc.Server
    httpServer    *http.Server
    metricsServer *http.Server
}
```

Key responsibilities:
- Lifecycle management for all components
- State synchronization across instances
- Certificate generation and rotation
- Event bus for real-time updates
- Health monitoring and auto-recovery

#### 2.2 API Implementation
Define gRPC services:
```proto
service polystackService {
    rpc ListComponents(ListComponentsRequest) returns (ListComponentsResponse);
    rpc InstallComponent(InstallComponentRequest) returns (stream InstallComponentResponse);
    rpc ConfigureComponent(ConfigureComponentRequest) returns (stream ConfigureComponentResponse);
    rpc UninstallComponent(UninstallComponentRequest) returns (stream UninstallComponentResponse);
    rpc StreamLogs(StreamLogsRequest) returns (stream LogEntry);
    rpc Health(google.protobuf.Empty) returns (HealthResponse);
}
```

### Phase 3: Deployment Engines (Days 11-20)

#### 3.1 Kubernetes Engine
Implement Kubernetes deployment using:
- Client-go for API interactions
- Helm SDK for chart management
- Watch API for real-time status
- Dynamic client for CRD support

#### 3.2 Docker Compose Engine
Implement Docker Compose support:
- Parse and generate compose files
- Container lifecycle management
- Volume and network handling
- Health check integration

#### 3.3 Air-Gap Engine
Implement offline deployment:
- Bundle creation with all dependencies
- Image packaging and loading
- Offline certificate generation
- Verification and integrity checks

### Phase 4: Component Library (Days 21-30)

#### 4.1 Component Definitions
Create component definitions for:
- PostgreSQL (with HA options)
- MinIO (S3-compatible storage)
- ClickHouse (analytics database)
- MLflow (ML platform)
- Argo Workflows
- Keycloak (authentication)
- Prometheus & Grafana
- And 10+ more components

Each component needs:
```yaml
name: postgresql
version: 15.1
dependencies:
  - cert-manager
configuration:
  username: ${POSTGRES_USER}
  password: ${POSTGRES_PASSWORD}
  database: ${POSTGRES_DB}
health_checks:
  - type: tcp
    port: 5432
  - type: exec
    command: ["pg_isready", "-U", "postgres"]
lifecycle:
  install: scripts/install.sh
  configure: scripts/configure.sh
  upgrade: scripts/upgrade.sh
  uninstall: scripts/uninstall.sh
```

### Phase 5: Security Implementation (Days 31-40)

#### 5.1 mTLS Everywhere
Implement mutual TLS:
- Certificate Authority setup
- Client/server cert generation
- Automatic rotation (30-day cycle)
- Zero-downtime rotation

#### 5.2 Secret Management
Integrate with HashiCorp Vault:
- AppRole authentication
- Dynamic secret generation
- Encryption at rest
- Audit logging

#### 5.3 RBAC Implementation
Role-based access control:
- User/service account management
- Permission policies (OPA)
- Audit trail for all operations
- Compliance reporting

### Phase 6: CLI and TUI (Days 41-50)

#### 6.1 CLI Commands
Implement CLI using Cobra:
```go
Commands:
- polystack install <component>
- polystack configure <component>
- polystack status
- polystack upgrade
- polystack uninstall
- polystack backup/restore
- polystack airgap create-bundle
- polystack airgap install-bundle
```

#### 6.2 TUI Implementation
Build interactive TUI with Bubble Tea:
- Dashboard view with system status
- Component list with health indicators
- Real-time log streaming
- Interactive configuration
- Progress indicators for operations

### Phase 7: Multi-Architecture Support (Days 51-60)

#### 7.1 Dagger Build Pipeline
Implement multi-arch builds:
```go
// dagger/pipeline/multiarch.go
platforms := []dagger.Platform{
    "linux/amd64",
    "linux/arm64",
    "linux/arm/v7",
    "windows/amd64",
}
```

#### 7.2 Platform Detection
Auto-detect and optimize for:
- CPU architecture
- Available features (AVX, NEON)
- Container runtime
- OS-specific optimizations

### Phase 8: Observability (Days 61-70)

#### 8.1 Metrics and Monitoring
Implement comprehensive monitoring:
- Prometheus metrics exposure
- Custom dashboards in Grafana
- Alert rules and notifications
- SLO/SLI tracking

#### 8.2 Distributed Tracing
OpenTelemetry integration:
- Request tracing across services
- Performance bottleneck detection
- Error tracking and analysis
- Correlation with logs

#### 8.3 Audit System
Complete audit trail:
- Every API call logged
- State changes tracked
- Immutable audit log
- Compliance report generation

### Phase 9: Testing and Quality (Days 71-80)

#### 9.1 Test Implementation
Comprehensive test suite:
- Unit tests (80% coverage minimum)
- Integration tests
- E2E tests with real clusters
- Performance benchmarks
- Security scanning

#### 9.2 CI/CD Pipeline
Automated pipeline:
- Multi-arch builds
- Automated testing
- Security scanning
- Release automation
- Documentation generation

### Phase 10: Documentation and Release (Days 81-90)

#### 10.1 Documentation
Complete documentation:
- Architecture overview
- Installation guide
- Component reference
- API documentation
- Troubleshooting guide
- Video tutorials

#### 10.2 Release Preparation
- Version tagging
- Release notes
- Migration guides
- Performance benchmarks
- Security audit

## Technical Implementation Details

### State Management
```go
type IStateManager interface {
    // Component state operations
    GetComponentState(ctx context.Context, name string) (*ComponentState, error)
    SetComponentState(ctx context.Context, state *ComponentState) error
    
    // Watch operations
    WatchComponent(ctx context.Context, name string) (<-chan StateChange, error)
    
    // Transaction support
    Transaction(ctx context.Context, fn TransactionFunc) error
}
```

### Lifecycle Management
```go
type ILifecycleManager interface {
    Install(ctx context.Context, component Component) error
    Configure(ctx context.Context, component Component, config Config) error
    Upgrade(ctx context.Context, component Component, version string) error
    Uninstall(ctx context.Context, component Component) error
    
    // Health monitoring
    CheckHealth(ctx context.Context, component Component) (Health, error)
}
```

### Certificate Management
```go
type ICertificateManager interface {
    GenerateCA(ctx context.Context) (*Certificate, error)
    IssueCertificate(ctx context.Context, subject Subject) (*Certificate, error)
    RotateCertificate(ctx context.Context, cert *Certificate) (*Certificate, error)
    
    // Auto-rotation
    StartAutoRotation(ctx context.Context, interval time.Duration)
}
```

### Air-Gap Bundle Structure
```
bundle.tar.gz
├── manifest.json
├── images/
│   ├── postgres-15.1.tar
│   ├── minio-latest.tar
│   └── ...
├── charts/
│   ├── postgresql/
│   ├── minio/
│   └── ...
├── binaries/
│   ├── polystack-linux-amd64
│   ├── polystack-linux-arm64
│   └── ...
└── configs/
    └── default-config.yaml
```

## Development Guidelines

### Code Standards
- Follow Go best practices
- Comprehensive error handling
- Structured logging (zap)
- Context propagation
- Graceful shutdown

### Security Standards
- No hardcoded secrets
- Input validation
- Rate limiting
- Audit everything
- Principle of least privilege

### Performance Standards
- Response time < 100ms
- Memory usage < 500MB
- Startup time < 5s
- Concurrent operations
- Resource pooling

## Testing Requirements

### Unit Tests
```go
func TestComponentInstall(t *testing.T) {
    // Test successful installation
    // Test failure scenarios
    // Test rollback capability
    // Test idempotency
}
```

### Integration Tests
- Real Kubernetes cluster
- Docker Compose environment
- Multi-node scenarios
- Network failures
- Security scenarios

### Performance Tests
- Load testing (1000+ components)
- Stress testing
- Memory leak detection
- CPU profiling
- Network bandwidth

## Delivery Checklist

□ All code implemented and tested
□ Documentation complete
□ Security audit passed
□ Performance benchmarks met
□ Multi-architecture builds working
□ Air-gap deployment verified
□ Enterprise features functional
□ Open source license applied
□ CI/CD pipeline operational
□ Release artifacts generated

## Success Criteria

1. **Functionality**: All features working as specified
2. **Performance**: Meets or exceeds benchmarks
3. **Security**: Passes security audit
4. **Usability**: Intuitive CLI/TUI interface
5. **Reliability**: 99.9% success rate
6. **Compatibility**: Works on all target platforms
7. **Documentation**: Comprehensive and clear
8. **Test Coverage**: >80% code coverage
9. **Build Time**: <5 minutes for all platforms
10. **Deployment Time**: <30 minutes for full stack

## Additional Resources

- Architecture diagrams: [Previous sections]
- API specifications: [Proto files]
- Component details: [YAML definitions]
- Security policies: [OPA rules]
- Test scenarios: [Test plans]

## Support During Implementation

For clarification on any aspect:
1. Refer to the architecture diagrams
2. Check the API specifications
3. Review security requirements
4. Validate against success criteria
5. Ensure enterprise readiness

This implementation should result in a production-ready, enterprise-grade tool that significantly simplifies Kubernetes deployments while maintaining security, reliability, and performance standards.

This comprehensive implementation guide provides:

1. **Clear Structure**: Step-by-step phases with specific deliverables
2. **Technical Details**: Code examples and architecture patterns
3. **Quality Standards**: Testing, security, and performance requirements
4. **Timeline**: 90-day implementation schedule
5. **Success Criteria**: Measurable outcomes


</user_query>