# polystack - Kubernetes Application Stack Tool

[![Build Status](https://gitlab.thalesdigital.io/*********************/spikes/polystack/workflows/CI/badge.svg)](https://gitlab.thalesdigital.io/*********************/spikes/polystack/actions)

polystack is an tool for managing Kubernetes application stacks with support for air-gapped deployments, multi-architecture builds, and comprehensive lifecycle management.

## Features

- 🚀 **Multi-Platform Support**: Kubernetes, Docker Compose, and bare metal deployments
- 🔒 **Air-Gapped Deployments**: Full offline installation support
- 🏗️ **Multi-Architecture**: AMD64, ARM64, and ARMv7 support
- 🎨 **Modern TUI**: Interactive terminal interface with real-time updates
- 🔐 **Security**: mTLS, certificate auto-rotation, and HashiCorp Vault integration
- 📊 **Observability**: Built-in metrics, logging, and tracing
- 🔄 **Lifecycle Management**: Install, configure, upgrade, and uninstall with zero downtime
- 🌍 **WSL Support**: Native Windows Subsystem for Linux integration

## Quick Start

### Installation

```bash
# Install polystack CLI
curl -sSL https://get.polystack.io | bash

# Start the daemon
sudo systemctl start polystackd

# Launch the TUI
polystack
```

### Docker Compose

```bash
# Clone the repository
git clone https://gitlab.thalesdigital.io/*********************/spikes/polystack.git
cd polystack

# Start with Docker Compose
docker-compose up -d

# Connect with CLI
polystack --daemon localhost:7777
```

### Air-Gapped Installation

```bash
# Create bundle on internet-connected machine
polystack airgap create-bundle -o polystack-bundle.tar.gz

# Transfer bundle to air-gapped environment
# Then install
polystack airgap install-bundle -b polystack-bundle.tar.gz
```

## Architecture

polystack follows a client-server architecture with a central daemon (`polystackd`) managing all operations:

- **polystackd**: Core daemon providing gRPC and REST APIs
- **polystack**: CLI and TUI client
- **State Management**: Distributed state with etcd and PostgreSQL
- **Security**: Certificate management with auto-rotation
- **Deployment Engines**: Pluggable engines for different platforms

## Documentation

- [Getting Started](docs/guides/getting-started.md)
- [Architecture Overview](docs/architecture/overview.md)
- [API Reference](docs/api/reference.md)
- [Deployment Guide](docs/deployment/guide.md)

## Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## License

polystack is licensed under the [Apache License 2.0](LICENSE).
