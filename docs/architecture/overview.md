# polystack Architecture Overview

## System Architecture

polystack is designed as a distributed, system for managing Kubernetes application stacks.

### Core Components

1. **polystack Daemon (polystackd)**
   - Central control plane
   - gRPC and REST API servers
   - State management
   - Event bus
   - Plugin system

2. **Client Layer**
   - CLI (`polystack`)
   - TUI (Terminal User Interface)
   - SDKs (Go, Python, JavaScript)

3. **Deployment Engines**
   - Kubernetes Engine
   - Docker Compose Engine
   - Air-Gap Engine
   - Bare Metal Engine

4. **Storage Backends**
   - etcd for distributed configuration
   - PostgreSQL for relational data
   - HashiCorp Vault for secrets
   - Local filesystem for cache

### Key Features

#### Multi-Architecture Support
- AMD64, ARM64, ARMv7
- Platform-specific optimizations
- Cross-compilation support

#### Security
- mTLS for all communications
- Certificate auto-rotation
- RBAC with policy enforcement
- Audit logging

#### High Availability
- Multi-master support
- State replication
- Automatic failover
- Self-healing

### Deployment Modes

1. **Kubernetes Mode**
   - Native Kubernetes integration
   - Helm chart support
   - Operator pattern

2. **Docker Compose Mode**
   - Simplified deployment
   - Single-node or multi-node
   - Persistent volumes

3. **Air-Gapped Mode**
   - Offline installation
   - Bundle creation
   - Local registry support

### API Design

polystack provides three API interfaces:

1. **gRPC API** (port 7777)
   - Primary API for clients
   - Streaming support
   - Efficient binary protocol

2. **REST API** (port 8080)
   - HTTP/JSON gateway
   - OpenAPI specification
   - Web-friendly

3. **WebSocket API** (port 8081)
   - Real-time updates
   - Event streaming
   - Bidirectional communication
