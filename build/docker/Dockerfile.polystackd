# Build stage
FROM golang:1.24-alpine AS builder

RUN apk add --no-cache git make

WORKDIR /build
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN make build-polystackd

# Runtime stage
FROM alpine:3.18

RUN apk add --no-cache ca-certificates tzdata

COPY --from=builder /build/dist/polystackd /usr/local/bin/polystackd

RUN adduser -D -u 1000 polystack && \
    mkdir -p /var/lib/polystack /etc/polystack && \
    chown -R polystack:polystack /var/lib/polystack /etc/polystack

USER polystack

EXPOSE 7777 8080 9090

ENTRYPOINT ["/usr/local/bin/polystackd"]
