# polystack Makefile
SHELL := /bin/bash

# Variables
REGISTRY ?= ghcr.io/$(GITHUB_ORG)
VERSION ?= $(shell git describe --tags --always --dirty)
PLATFORMS ?= linux/amd64,linux/arm64
GOARCH ?= $(shell go env GOARCH)
GOOS ?= $(shell go env GOOS)

# Go parameters
GOCMD = go
GOBUILD = $(GOCMD) build
GOTEST = $(GOCMD) test
GOMOD = $(GOCMD) mod
GOVET = $(GOCMD) vet
GOFMT = gofmt

# Directories
CMD_DIR = ./cmd
PKG_DIR = ./pkg
INTERNAL_DIR = ./internal
BUILD_DIR = ./build
DIST_DIR = ./dist

# Binaries
polystackD_BINARY = polystackd
polystack_BINARY = polystack
INSTALLER_BINARY = polystack-installer

# Targets
.PHONY: all build test clean

all: test build

## Build targets
build: build-polystackd build-polystack build-installer

build-polystackd:
	@echo "Building polystackd..."
	@CGO_ENABLED=0 $(GOBUILD) -ldflags "-X main.version=$(VERSION)" -o $(DIST_DIR)/$(polystackD_BINARY) $(CMD_DIR)/polystackd

build-polystack:
	@echo "Building polystack..."
	@CGO_ENABLED=0 $(GOBUILD) -ldflags "-X main.version=$(VERSION)" -o $(DIST_DIR)/$(polystack_BINARY) $(CMD_DIR)/polystack

build-installer:
	@echo "Building polystack-installer..."
	@CGO_ENABLED=0 $(GOBUILD) -ldflags "-X main.version=$(VERSION)" -o $(DIST_DIR)/$(INSTALLER_BINARY) $(CMD_DIR)/polystack-installer

## Test targets
test:
	@echo "Running tests..."
	@$(GOTEST) -v -race -coverprofile=coverage.out ./...

test-unit:
	@echo "Running unit tests..."
	@$(GOTEST) -v -short ./...

test-integration:
	@echo "Running integration tests..."
	@$(GOTEST) -v -tags=integration ./test/integration/...

test-e2e:
	@echo "Running e2e tests..."
	@$(GOTEST) -v -tags=e2e ./test/e2e/...

## Code quality
fmt:
	@echo "Formatting code..."
	@$(GOFMT) -s -w .

vet:
	@echo "Running go vet..."
	@$(GOVET) ./...

lint:
	@echo "Running golangci-lint..."
	@golangci-lint run

## Container targets
docker-build:
	@echo "Building Docker images..."
	@docker buildx build --platform $(PLATFORMS) -t $(REGISTRY)/polystackd:$(VERSION) -f build/docker/Dockerfile.polystackd .
	@docker buildx build --platform $(PLATFORMS) -t $(REGISTRY)/polystack:$(VERSION) -f build/docker/Dockerfile.polystack .

docker-push:
	@echo "Pushing Docker images..."
	@docker buildx build --push --platform $(PLATFORMS) -t $(REGISTRY)/polystackd:$(VERSION) -f build/docker/Dockerfile.polystackd .
	@docker buildx build --push --platform $(PLATFORMS) -t $(REGISTRY)/polystack:$(VERSION) -f build/docker/Dockerfile.polystack .

## Dagger targets
dagger-build:
	@echo "Building with Dagger..."
	@go run ./dagger/cmd/build

## Proto generation
proto:
	@echo "Generating protobuf files..."
	@buf generate

## Dependencies
deps:
	@echo "Downloading dependencies..."
	@$(GOMOD) download

tidy:
	@echo "Tidying dependencies..."
	@$(GOMOD) tidy

## Clean
clean:
	@echo "Cleaning..."
	@rm -rf $(DIST_DIR) $(BUILD_DIR) coverage.out

## Help
help:
	@echo "Available targets:"
	@grep -E "^[a-zA-Z_-]+:.*?## .*$$" $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

.DEFAULT_GOAL := help
